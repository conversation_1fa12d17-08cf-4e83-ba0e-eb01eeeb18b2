<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="عاشر جوبز يربط المرشحين بفرص العمل المناسبة ويوفر تدريبًا لمسؤولي التوظيف لتحسين عمليات التوظيف.">
    <meta name="keywords" content="وظائف, توظيف, فرص عمل, تدريب وظيفي, عاشر جوبز, مطابقة الموظفين, تدريب مسؤولي التوظيف, النمو المهني">
    <meta name="author" content="عاشر جوبز - وظائف العاشر">
    <meta name="geo.region" content="EG">
    <meta name="geo.placename" content="مدينة العاشر من رمضان">
    <meta name="geo.position" content="30.2967;31.7433">
    <meta name="ICBM" content="30.2967, 31.7433">
    
     <!-- Favicon -->
    <link rel="icon" href="src/images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="src/images/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="src/images/favicon.ico">
    <meta name="theme-color" content="#003C43">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Main custom css -->
    <link href="src/css/custom.css" rel="stylesheet" media="screen">
    <link href="src/css/custom-ar.css" rel="stylesheet" media="screen">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- Google Ads -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2136749359648243" crossorigin="anonymous"></script>
    <script async custom-element="amp-auto-ads" src="https://cdn.ampproject.org/v0/amp-auto-ads-0.1.js"></script>
    
    
    <!-- Countdown Script -->
    <script src="public/js/countdown.js" defer></script>
    <style>
        /* RTL specific fixes for Arabic layout */
        body {
            direction: rtl;
            text-align: center;
            font-family: 'Cairo', sans-serif;
        }
        
        /* Container fixes for RTL */
        .contact-section, .contact-form-new2, .comming-soon {
            width: 100% !important;
            max-width: 800px !important;
            margin-right: auto !important;
            margin-left: auto !important;
            text-align: center !important;
            float: none !important;
            position: static !important;
            right: auto !important;
            left: auto !important;
            transform: none !important;
        }
        
        /* Force parent containers to center their children */
        body > div, .container {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
        }
        
        /* Text alignment fixes for RTL */
        .article-content, 
        .article-content p, 
        .hidden-content, 
        h1, h2, h3, h4, h5, h6, 
        p, li {
            text-align: center !important;
        }
        
        /* List styling for RTL */
        .hidden-content ul {
            padding-right: 0 !important;
            padding-left: 0 !important;
            margin-right: auto !important;
            margin-left: auto !important;
            display: inline-block;
            text-align: center !important;
        }
        
        .hidden-content ul li {
            text-align: center !important;
            list-style-position: inside;
        }
        
        /* Button alignment for RTL */
        .read-more-btn {
            display: block;
            margin: 15px auto !important;
        }
        
        /* Fix for floating sitemap in RTL */
        .floating-sitemap {
            left: 20px !important;
            right: auto !important;
        }
        
        .dropdown-menu {
            left: 0 !important;
            right: auto !important;
            text-align: center !important;
        }
        
        .dropdown-menu ul {
            padding-right: 0 !important;
            text-align: center !important;
        }
        
        .dropdown-menu li {
            text-align: center !important;
        }
        
        /* Footer alignment for RTL */
        .footer-content {
            text-align: center !important;
        }
        
        .footer-links {
            padding-right: 0 !important;
            text-align: center !important;
        }
        
        .footer-social {
            text-align: center !important;
        }
        
        /* Center all section content */
        .hero-section,
        .whatsapp-section,
        .partners-grid-section,
        .contact-section,
        .contact-form,
        .contact-form-new,
        .contact-form-new2,
        .contact-form-new3,
        .contact-form-new4,
        .contact-box,
        .article-content,
        .hidden-content {
            text-align: center !important;
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
        }
        
        /* Center all images */
        .pic, 
        .image-container, 
        .video-container,
        .partner-logo-container {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin-left: auto !important;
            margin-right: auto !important;
        }
        
        /* Center all titles and headings */
        .title, 
        .title2, 
        .title3, 
        .title4, 
        .section-title {
            text-align: center !important;
            width: 100% !important;
        }
        
        /* Ensure all paragraphs are centered */
        p {
            text-align: center !important;
            width: 100% !important;
            max-width: 800px !important;
            margin-left: auto !important;
            margin-right: auto !important;
        }
        
        /* Center all buttons */
        .btn, 
        button, 
        .read-more-btn, 
        .hero-cta {
            margin-left: auto !important;
            margin-right: auto !important;
            text-align: center !important;
            display: block !important;
        }
    </style>
</head>
<body>
    <amp-auto-ads type="adsense" data-ad-client="ca-pub-2136749359648243"></amp-auto-ads>

    <!-- Header with Logo -->
    <header class="site-header">
        <div class="container">
            <div class="header-content">
                <div class="site-logo">
                    <a href="index.html">
                        <!-- Fixed image path -->
                        <img src="src/images/logo.png" alt="شعار عاشر جوبز" height="100">
                    </a>
                </div>
                
                <!-- Language Switcher - Fixed link to English version -->
                <div class="language-switcher">
                    <a href="src/html/indexar.html" class="lang-switch-btn">
                        <span class="lang-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path fill="none" d="M0 0h24v24H0z"/>
                                <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-2.29-2.333A17.9 17.9 0 0 1 8.027 13H4.062a8.008 8.008 0 0 0 5.648 6.667zM10.03 13c.151 2.439.848 4.73 1.97 6.752A15.905 15.905 0 0 0 13.97 13h-3.94zm9.908 0h-3.965a17.9 17.9 0 0 1-1.683 6.667A8.008 8.008 0 0 0 19.938 13zM4.062 11h3.965A17.9 17.9 0 0 1 9.71 4.333 8.008 8.008 0 0 0 4.062 11zm5.969 0h3.938A15.905 15.905 0 0 0 12 4.248 15.905 15.905 0 0 0 10.03 11zm4.259-6.667A17.9 17.9 0 0 1 15.938 11h3.965a8.008 8.008 0 0 0-5.648-6.667z"/>
                            </svg>
                        </span>
                        <span class="lang-text">English</span>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero section with clear value proposition -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1>ربط المواهب بالفرص في مدينة العاشر من رمضان</h1>
                <p>عاشر جوبز تسد الفجوة بين المواهب والفرص، حيث تساعد الشركات في العثور على المرشحين المثاليين وتُمكّن الباحثين عن عمل من تطوير مساراتهم المهنية.</p>
                
               <div class="hero-cta">
                    <a href="src/html/jobs.html" class="btn btn-primary">تصفح الوظائف</a>
                    
                </div>
            </div>
        </div>
    </section>

    <!-- Floating Sitemap - Fixed RTL positioning -->
    <div class="floating-sitemap">
        <button class="sitemap-toggle">☰</button>
        <div class="dropdown-menu">
            <h3>خريطة الموقع</h3>
            <ul>
                <p>اختصارات المقالات</p>
                <li><a href="#job-tips">⦁ كيف تحصل على قبول في وظيفة أحلامك؟</a></li>
                <li><a href="#resume-guide">⦁ مكونات السيرة الذاتية الاحترافية</a></li>
                <li><a href="#video-guide">⦁ ما هو نظام تتبع المتقدمين (ATS)؟</a></li>
                <li><a href="#a-hunt">⦁ خدمة البحث عن المواهب</a></li>
            </ul>
        </div>
    </div>
    
    <!-- WhatsApp Channels Section -->
    <section class="whatsapp-section">

         <div class="wacontainer">
            <h2 class="title2">
                <i class="fa fa-whatsapp" aria-hidden="true"></i> 
                انضم إلى قناة الواتساب الخاصة بنا للحصول على أحدث الوظائف في العاشر من رمضان
            </h2>
            <div class="image-container">
                <a href="https://www.whatsapp.com/channel/0029Vb8hBqrFCCocCAxIi50L">
                    <div class="qr"></div>
                </a>
            </div>        
        </div>
        
        <div class="container">
            <h2>هل أنت طالب جامعي؟ انضم إلى قناة الواتساب للحصول على وظائف التدريب.</h2>
            <div class="wacontainer">
                <h2 class="title2">
                    <i class="fa fa-whatsapp" aria-hidden="true"></i> 
                    قناة التدريب والوظائف (المهندسين)
                </h2>
                <div class="image-container">
                    <a href="https://whatsapp.com/channel/0029VbAQ8IO1yT21jcZBNN0g">
                        <div class="qr2"></div>
                    </a>
                </div>        
            </div>
            <div class="wacontainer">
                <h2 class="title2">
                    <i class="fa fa-whatsapp" aria-hidden="true"></i> 
                    قناة التدريب والوظائف (العلوم والصيدلة والزراعة)
                </h2>
                <div class="image-container">
                    <a href="https://whatsapp.com/channel/0029Vb62J9X2ZjCr5CSyOC1J">
                        <div class="qr3"></div>
                    </a>
                </div>        
            </div>
            <div class="wacontainer">
                <h2 class="title2">
                    <i class="fa fa-whatsapp" aria-hidden="true"></i> 
                    قناة التدريب والوظائف (المطورين)
                </h2>
                <div class="image-container">
                    <a href="https://whatsapp.com/channel/0029Vb5gEOm5q08TOzQbU215">
                        <div class="qr4"></div>
                    </a>
                </div>        
            </div>

           
        </div>
    </section>

    <!-- Articles Section -->
    <div class="comming-soon fade-in">
        <!-- Job Tips Article -->
        <div class="contact-section fade-in">
            <div class="contact-form"> 
                <div class="contact-box">
                    <h2 class="title" id="job-tips">كيف تحصل على قبول في وظيفة أحلامك؟</h2>
                    <div class="article-content">
                        <p>
                            يتساءل الكثير من الناس لماذا لم يتلقوا ردًا بعد إرسال سيرتهم الذاتية. إليك بعض النصائح التي قد تساعدك في الحصول على مكالمة للمقابلة:
                        </p>
                        <p>
                            <strong>1- سيرة ذاتية لكل شركة:</strong> لا يمكن أن تستمر في استخدام سيرة ذاتية واحدة فقط للتقدم لوظائفك. تحتاج إلى تعديل سيرتك الذاتية في كل مرة تتقدم فيها لوظيفة. ستفهم السبب بعد قراءة النصائح التالية.
                        </p>
                        
                        <div class="hidden-content">
                            <p>
                                <strong>2- تبسيط سيرتك الذاتية:</strong> يعتقد بعض الناس أنه من الجيد إنشاء سيرة ذاتية ملونة لجذب انتباه الشخص الذي يقرأها، ولكن هذا قد يمنعها من اجتياز أنظمة تتبع المتقدمين (ATS).
                            </p>
                            <div class="pic">
                                <!-- Fixed image path -->
                                <img src="src/images/Apg.jpg" alt="مثال على السيرة الذاتية">
                            </div>
                            <p>
                                <strong>3- اجتياز نظام تتبع المتقدمين (ATS):</strong> هذه الأنظمة العملاقة غالبًا ما تقتل أحلامك.
                            </p>
                            <p><strong>كيف تعمل:</strong></p>
                            <p>
                                <strong>أ - جمع الطلبات:</strong> يقوم نظام تتبع المتقدمين بجمع طلبات الوظائف المقدمة عبر الإنترنت من خلال مواقع الوظائف أو مواقع الشركات أو رسائل البريد الإلكتروني.
                            </p>
                            <p><strong>ب - تحليل السيرة الذاتية:</strong> يقوم بفحص السير الذاتية لاستخراج وتنظيم المعلومات الرئيسية مثل معلومات الاتصال وخبرة العمل والتعليم والمهارات.</p>
                            <p><strong>ج - مطابقة الكلمات المفتاحية:</strong> يقارن النظام السير الذاتية مع أوصاف الوظائف لتحديد المرشحين الذين تتطابق مؤهلاتهم بشكل وثيق مع الدور المطلوب، وهذا هو السبب في أنك تحتاج إلى سيرة ذاتية مخصصة لكل شركة.</p>
                            <p><strong>د - تصنيف المرشحين:</strong> بناءً على المطابقة، يقوم النظام بتسجيل أو تصنيف السير الذاتية، مع تسليط الضوء على المرشحين الأكثر ملاءمة للمراجعة من قبل مديري التوظيف.</p>
                            <p><strong>كيفية اجتياز نظام تتبع المتقدمين:</strong> ببساطة تجنب استخدام سيرة ذاتية ملونة واستخدم الكلمات المفتاحية الموجودة في وصف الوظيفة. على سبيل المثال، إذا كان وصف الوظيفة يذكر "قائد فريق"، استخدم "قائد فريق" في قسم الخبرة بدلاً من "مدير".</p>
                        </div>
                        <button class="read-more-btn">اقرأ المزيد</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Resume Guide Article -->
        <div class="contact-section fade-in">
            <div class="contact-form"> 
                <div class="contact-box">
                    <h2 class="title" id="resume-guide">مكونات السيرة الذاتية الاحترافية</h2>
                    <div class="article-content">
                        <p>
                            السيرة الذاتية المنظمة جيدًا هي تذكرتك للفت انتباه أصحاب العمل. فيما يلي المكونات الأساسية التي يجب أن تتضمنها كل سيرة ذاتية احترافية:
                        </p>
                        <p>
                            <strong>1- معلومات الاتصال:</strong> قم بتضمين اسمك الكامل ورقم هاتفك وعنوان بريدك الإلكتروني المهني وملف تعريف LinkedIn الخاص بك. تأكد من أن هذه المعلومات حديثة ومرئية بسهولة في أعلى سيرتك الذاتية.
                        </p>
                        
                        <div class="hidden-content">
                            <p>
                                <strong>2- ملخص مهني:</strong> نبذة موجزة من 2-3 جمل تسلط الضوء على خلفيتك المهنية ونقاط قوتك الرئيسية وأهدافك المهنية. يجب تخصيص هذا لكل وظيفة تتقدم لها.
                            </p>
                            <div class="pic">
                                <!-- Fixed image path -->
                                <img src="src/images/resume-example.jpg" alt="مثال على السيرة الذاتية الاحترافية">
                            </div>
                            <p>
                                <strong>3- خبرة العمل:</strong> اذكر تاريخ عملك بترتيب زمني عكسي (الأحدث أولاً). لكل منصب، قم بتضمين:
                            </p>
                            <ul>
                                <li>اسم الشركة وموقعها</li>
                                <li>المسمى الوظيفي الخاص بك</li>
                                <li>تواريخ التوظيف (شهر/سنة)</li>
                                <li>3-5 نقاط تصف مسؤولياتك وإنجازاتك</li>
                                <li>نتائج قابلة للقياس كلما أمكن ذلك (مثل "زيادة المبيعات بنسبة 25٪")</li>
                            </ul>
                            <p>
                                <strong>4- التعليم:</strong> اذكر مؤهلاتك التعليمية، بما في ذلك اسم المؤسسة والدرجة العلمية وسنة التخرج.
                            </p>
                            <p>
                                <strong>5- المهارات:</strong> قم بإدراج المهارات ذات الصلة بالوظيفة، مع التركيز على المهارات التقنية والشخصية المطلوبة في وصف الوظيفة.
                            </p>
                            <p>
                                <strong>6- الإنجازات والشهادات:</strong> قم بتضمين أي شهادات مهنية أو جوائز أو إنجازات بارزة ذات صلة بمجال عملك.
                            </p>
                        </div>
                        <button class="read-more-btn">اقرأ المزيد</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- ATS Video Guide -->
        <div class="contact-section fade-in">
            <div class="contact-form-new">
                <div class="contact-box">
                    <h2 class="title" id="video-guide">شاهد الفيديو أدناه لمعرفة المزيد عن نظام تتبع المتقدمين (ATS)</h2>
                    <div class="video-container">
                        <iframe 
                            src="https://www.youtube.com/embed/fYd2i5zOZZE" 
                            title="كيفية اجتياز نظام تتبع المتقدمين والحصول على وظيفة أحلامك" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    </div>
                    <p>انتظر... هذا ليس كل شيء، قم بالتمرير لأسفل لمعرفة المزيد عن السير الذاتية.</p>
                </div>
            </div>
        </div>

        <!-- A Hunt Service -->
        <div class="contact-form-new2 fade-in">
            <div class="contact-box">
                
                  
                    <h2 class="title" id="a-hunt">هل تحتاج إلى مرشح مسؤول لعملك؟ قد تحتاج إلى خدمة A Hunt!</h2>
                      <div class="pic">
                        <img src="src/images/apg3.jpg" alt="خدمة A Hunt">
                    </div>
                    <div class="article-content">
                        <p><strong>مقدمة عن خدمة A Hunt:</strong> خدمة A Hunt هي خدمة توظيف حصرية مصممة لتحديد واستقطاب وتأمين أفضل المواهب لمؤسستك. باستخدام شبكتنا الواسعة وخوارزميات مطابقة الخبرات المتقدمة واستراتيجيات التوظيف المخصصة، نضمن تقديم أنسب المرشحين لاحتياجاتك المحددة. من البحث والفرز وحتى التوظيف النهائي، يتعامل فريق خدمة A Hunt مع عملية التوظيف بأكملها بدقة واحترافية.</p>

                        <p><strong>لماذا تختار خدمة A Hunt؟</strong></p>
                        <div class="hidden-content">
                            <p><strong>حلول توظيف مخصصة:</strong> نحن نعطي الأولوية لفهم ثقافة شركتك وقيمها واحتياجاتها الفريدة، مما يضمن توافق المرشحين مع أهداف مؤسستك.</p>
                            <p><strong>كفاءة الوقت والتكلفة:</strong> تعمل عمليتنا المبسطة على توفير وقتك ومواردك الثمينة، مما يتيح لك التركيز على أنشطة العمل الأساسية بينما نتعامل نحن مع تعقيدات التوظيف.</p>
                            <p><strong>الوصول إلى مواهب استثنائية:</strong> من خلال الاستفادة من شبكتنا الواسعة وخبرتنا، نوفر لك مجموعة من المرشحين المؤهلين تأهيلاً عال<|im_start|> الذين قد لا يكونون متاحين في سوق العمل ولكنهم مناسبون تمام السلطاتك.</p>
                            <p><strong>متابعة مخصصة:</strong> نقف بجانبك حتى يتم شغل المنصب بنجاح، مع توفير متابعة مستمرة ودعم لتجربة توظيف سلسة.</p>

                            <p><strong>العمولة والشروط</strong></p>
                            <p>نقدم خدمتنا على أساس عمولة تنافسية لضمان حصولك على قيمة استثنائية:</p>
                            <p><strong>رسوم العمولة:</strong> عند التعيين الناجح لمرشح، يتم فرض رسوم تعادل راتب شهري إجمالي للفرد المعين.</p>
                            <p><strong>الدفعة الأولية:</strong> لبدء عملية التوظيف، نطلب دفعة أولية بنسبة 10٪ من الراتب الشهري الإجمالي للوظيفة المطلوبة. تُظهر هذه الدفعة التزامك وتسمح لنا بتخصيص الموارد اللازمة لعملية البحث.</p>
                        </div>
                        <button class="read-more-btn">اقرأ المزيد</button>
                    </div>
                </div>
            </div>
        </div>
    

    <!-- Partners Grid Section -->
    <section class="partners-grid-section">
        <div class="container">
            <h2 class="section-title">شركاؤنا</h2>
            <div class="partners-grid">
                <!-- Partner 1 -->
                <div class="partner-grid-item">
                    <a href="https://www.instagram.com/maskan.architecture.egypt?igsh=YXh0ajd4dTFjcTJ4" target="_blank" rel="noopener noreferrer" aria-label="مسكن للهندسة المعمارية مصر">
                        <div class="partner-logo-container">
                            <img src="src/images/partner1.png" alt="مسكن للهندسة المعمارية مصر" class="partner-grid-logo" loading="lazy">
                        </div>
                    </a>
                </div>
                
                <!-- Partner 2 -->
                <div class="partner-grid-item">
                    <a href="https://www.instagram.com/linkout.eg?igsh=MWVqZGVtbWJtZGVkbw==" target="_blank" rel="noopener noreferrer" aria-label="لينك أوت مصر">
                        <div class="partner-logo-container">
                            <img src="src/images/partner2.png" alt="لينك أوت مصر" class="partner-grid-logo" loading="lazy">
                        </div>
                    </a>
                </div>
                
                <!-- Partner 3 -->
                <div class="partner-grid-item">
                    <a href="https://www.instagram.com/iti_egypt?igsh=MWVqZGVtbWJtZGVkbw==" target="_blank" rel="noopener noreferrer" aria-label="معهد تكنولوجيا المعلومات مصر">
                        <div class="partner-logo-container">
                            <img src="src/images/partner3.png" alt="معهد تكنولوجيا المعلومات مصر" class="partner-grid-logo" loading="lazy">
                        </div>
                    </a>
                </div>
                
                <!-- Partner 4 -->
                <div class="partner-grid-item">
                    <a href="https://www.instagram.com/iti_egypt?igsh=MWVqZGVtbWJtZGVkbw==" target="_blank" rel="noopener noreferrer" aria-label="شريك 4">
                        <div class="partner-logo-container">
                            <img src="src/images/partner4.png" alt="شريك 4" class="partner-grid-logo" loading="lazy">
                        </div>
                    </a>
                </div>
                
                <!-- Partner 5 -->
                <div class="partner-grid-item">
                    <a href="#" target="_blank" rel="noopener noreferrer" aria-label="شريك 5">
                        <div class="partner-logo-container">
                            <img src="src/images/partner5.png" alt="شريك 5" class="partner-grid-logo" loading="lazy">
                        </div>
                    </a>
                </div>
                
               
                
               
            </div>
        </div>
    </section>

      <!-- Footer: Modern, Accessible, and Responsive (English) -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-brand">
                <a href="index.html" class="footer-logo" aria-label="Asher Jobs Home">
                    <img src="src/images/logo.png" alt="Asher Jobs Logo" height="60" loading="lazy">
                    <h3>عاشر جوبز</h3>
                </a>
               
                <p class="footer-tagline">نربط المواهب بالفرص</p>
            </div>
            <nav class="footer-nav" aria-label="Footer Navigation">
                <ul class="footer-links">
                    <li><a href="src/html/jobs.html">تصفح الوظائف</a></li>
                    <li><a href="src/html/adminLogin.html">تسجيل دخول المسؤول</a></li>
                    <li><a href="src/html/contact.html">تواصل معنا</a></li>
                    <li><a href="src/html/privacy.html">سياسة الخصوصية</a></li>
                </ul>
            </nav>
            <div class="footer-social">
                <div class="social-icons">
                    <a href="https://www.facebook.com/profile.php?id=61566379346391" aria-label="Facebook" class="fa fa-facebook" tabindex="0"></a>
                    <a href="https://www.linkedin.com/company/3ashr-jobs/?viewAsMember=true" aria-label="LinkedIn" class="fa fa-linkedin" tabindex="0"></a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 Asher Jobs. All rights reserved.</p>
        </div>
  <style>
    /* Basic Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.site-footer {
  background-color: #003C43;
  color: #f1f1f1;
  padding: 40px 20px 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2rem;
}

.footer-brand {
  flex: 1 1 250px;
}

.footer-brand h3 {
  font-size: 1.5rem;
  margin-top: 10px;
  color: #ffffff;
}

.footer-tagline {
  font-size: 0.95rem;
  color: #cfcfcf;
  margin-top: 5px;
}

.footer-logo img {
  max-width: 100%;
  height: 100px;
  border-radius: 5px;
}

.footer-nav {
  flex: 1 1 200px;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-top: 20px;
}

.footer-links a {
  color: #cfcfcf;
  display: inline-block;
  transition: transform 0.3s ease;
}

.footer-links a:hover,
.footer-links a:focus {
  color: #77B0AA;
  text-decoration: none;
    transform: scale(1.1);
}

.footer-social {
  flex: 1 1 200px;
  margin-top: 70px;
}

.footer-social p {
  font-weight: bold;
  margin-bottom: 10px;
}

.social-icons a {
  display: inline-block;
  font-size: 1.25rem;
  margin-right: 15px;
  color: #cfcfcf;
  transition: color 0.3s, transform 0.3s;
}

.social-icons a:hover,
.social-icons a:focus {
  color: #ffffff;
  transform: scale(1.1);
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  font-size: 0.875rem;
  color: #999;
  border-top: 1px solid #444;
  margin-top: 30px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .footer-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .footer-social, .footer-brand, .footer-nav {
    flex: 1 1 100%;
  }

  .social-icons a {
    margin: 0 10px;
  }
}

  </style>
    </footer>

    <!-- Scripts -->
    <script>
        // Add error handling to detect and report JS issues
        window.addEventListener('error', function(event) {
            console.error('JavaScript error detected:', event.error);
            
            // Make all content visible if there's a JS error
            document.querySelectorAll('.fade-in').forEach(el => {
                el.classList.add('visible');
            });
            
            // Check for specific issues
            if (event.error && event.error.message && 
                (event.error.message.includes('undefined') || 
                 event.error.message.includes('null'))) {
                console.warn('Possible DOM element not found. Ensuring all content is visible.');
                document.body.style.visibility = 'visible';
                document.body.style.opacity = '1';
            }
        });

        document.addEventListener("DOMContentLoaded", function() {
            // Sitemap toggle functionality
            const sitemapToggle = document.querySelector('.sitemap-toggle');
            const dropdownMenu = document.querySelector('.dropdown-menu');
            
            if (sitemapToggle && dropdownMenu) {
                sitemapToggle.addEventListener('click', function() {
                    dropdownMenu.classList.toggle('show');
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', function(event) {
                    if (!event.target.closest('.floating-sitemap')) {
                        dropdownMenu.classList.remove('show');
                    }
                });
            }

            // Read More buttons functionality
            const readMoreButtons = document.querySelectorAll('.read-more-btn');
            readMoreButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const hiddenContent = this.previousElementSibling;
                    hiddenContent.classList.toggle('show');
                    this.textContent = hiddenContent.classList.contains('show') ? 'اقرأ أقل' : 'اقرأ المزيد';
                });
            });
        });
    </script>
    <script>
        // Improved fade-in animation handler
        document.addEventListener("DOMContentLoaded", function() {
            // First make sure all content is visible in case of JS errors
            setTimeout(() => {
                document.querySelectorAll('.fade-in').forEach(el => {
                    if (!el.classList.contains('visible')) {
                        el.classList.add('visible');
                    }
                });
            }, 1000); // Fallback after 1 second
            
            // Then set up proper intersection observer
            try {
                const fadeInElements = document.querySelectorAll(".fade-in");
                
                if ('IntersectionObserver' in window) {
                    const observer = new IntersectionObserver((entries) => {
                        entries.forEach((entry) => {
                            if (entry.isIntersecting) {
                                entry.target.classList.add("visible");
                            }
                        });
                    }, {
                        threshold: 0.1,
                        rootMargin: '50px'
                    });
                    
                    fadeInElements.forEach((element) => observer.observe(element));
                } else {
                    // Fallback for browsers without IntersectionObserver
                    fadeInElements.forEach(element => element.classList.add('visible'));
                }
            } catch (error) {
                console.error("Error in fade-in animation:", error);
                // Make all elements visible if there's an error
                document.querySelectorAll('.fade-in').forEach(el => el.classList.add('visible'));
            }
        });
    </script>
    <script src="../js/partnersCarousel.js"></script>
    
    <script>
        // Force center alignment for all content elements
        document.addEventListener('DOMContentLoaded', function() {
            // Apply centering to all major containers
            const containers = document.querySelectorAll('.container, section, .hero-content, .article-content, .wacontainer, .hidden-content, .contact-box');
            containers.forEach(container => {
                container.style.marginLeft = 'auto';
                container.style.marginRight = 'auto';
                container.style.textAlign = 'center';
                
                // Center flex children
                if (window.getComputedStyle(container).display === 'flex') {
                    container.style.justifyContent = 'center';
                    container.style.alignItems = 'center';
                }
            });
            
            // Center all headings and paragraphs
            const headingsAndParagraphs = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p');
            headingsAndParagraphs.forEach(element => {
                element.style.textAlign = 'center';
            });
            
            // Center all lists
            const lists = document.querySelectorAll('ul, ol');
            lists.forEach(list => {
                list.style.textAlign = 'center';
                list.style.paddingRight = '0';
                list.style.paddingLeft = '0';
                
                // Center list items
                const listItems = list.querySelectorAll('li');
                listItems.forEach(item => {
                    item.style.textAlign = 'center';
                    item.style.listStylePosition = 'inside';
                });
            });
            
            // Center all buttons
            const buttons = document.querySelectorAll('.btn, button, .read-more-btn');
            buttons.forEach(button => {
                button.style.marginLeft = 'auto';
                button.style.marginRight = 'auto';
                button.style.display = 'block';
                button.style.textAlign = 'center';
            });
            
            // Center all images
            const images = document.querySelectorAll('img, .pic, .image-container');
            images.forEach(image => {
                if (image.parentElement) {
                    image.parentElement.style.display = 'flex';
                    image.parentElement.style.justifyContent = 'center';
                    image.parentElement.style.alignItems = 'center';
                }
            });
        });
    </script>
</body>
</html>




















