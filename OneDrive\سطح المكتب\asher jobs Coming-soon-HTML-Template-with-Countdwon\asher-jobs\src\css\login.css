
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.container {
  width: 80%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1; /* Allows it to take remaining space */
}

.logo1 {
  background-image: url("logo1.svg");
  background-size: cover;
  width: 70px;
  height: 70px;

}
/* Header styles */
      header {
          background: #333;
 
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  
          border-bottom: 3px solid hwb(140 4% 13%);
          display: flex;
          align-items: center;
          width: 100%;
          position:fixed;
          top: 0;
          left: 0;
          z-index: 1000;
          
      }

      header a {
          color: #fff;
          text-decoration: none;
      }

      header nav {
          display: flex;
          width: 100%;
      }

      header ul {
          list-style: none;
          display: flex;
          align-items: center;
          width: 100%;
          padding: 0;
          margin: 0;
      }

      header ul li {
          padding: 0 10px;
          display: flex;
          align-items: center;
      }
      header ul li.right {
margin-left: auto;
      }
     

      header ul li.right .lang {
          display:flex;
          gap: 10px; /* Add space between AR and EN links */
      }
      

/* Wrapper styles */
.wrapper {
  width: 420px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(70px);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  color: #fff;
  border-radius: 10px;
  padding: 30px 80px;
  margin: auto;
  position: relative;
  align-items: center;
  text-align: center;
}

.wrapper h1 {
  font-size: 36px;
  text-align: center;
}
.wrapper .input-box{
  position:relative;
  width: 100%;
  height: 50px;
  margin: 20px -10px;
  padding:0 0px 0 0;
}


.input-box input {
  width: 100%;
  height: 100%;
  background: transparent;
  border: none;
  outline: none;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 40px;
  font-size: 16px;
  color: #fff;
  padding: 0px 45px 0px 20px;
}

.input-box input::placeholder {
  color: #fff;
}

.input-box i {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
}

.remember-forget {
  display: flex;
  justify-content: space-between;
  font-size: 14.5px;
  margin: -15px 0 15px;
}

.remember-forget label input {
  accent-color: #fff;
  margin-right: 3px;
}

.remember-forget a {
  color: #fff;
  text-decoration: none;
}

.remember-forget a:hover {
  text-decoration: underline;
}

.btn {
  width: 100%;
  height: 45px;
  background: #003C43;
  border: none;
  outline: none;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  font-size: 16px;
  color: #fff;
  font-weight: 600;
}

.btn:hover {
  background: #77B0AA;
  color:#fff;
  border-radius: 5px;
  box-shadow: 0 0 5px  #fff,
   0 0 25px  #fff,
    0 0 50px  #fff;
} 

body {
  font-size: 16px;
  height: 100%;
  font-weight: 100;
  font-family: "Montserrat-Arabic";
  background-color: #003C43;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.logo1 {
	background-image: url("../images/logo1.svg");
	background-size: cover;
	width: 70px;
	height: 70px;
	margin: 0 0;
	margin-bottom: 10px;
	border-radius: 5px;
}

    /* Footer styles */
    footer {
      background: #003C43;
      color: #fff;
      padding: 10px 0;
      border-top: 3px solid #77B0AA;
      margin-top: 50px;
      width: 100%;
    }

	.footer-container {
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 80%;
			margin: auto;
	}

	.footer-container section {
			margin-right: 20px;
	}

  /* Footer Adjustments */
.footer-container {
  text-align: center;
  align-items: center;
}
.footer-container section {
  margin-bottom: 30px;
}

