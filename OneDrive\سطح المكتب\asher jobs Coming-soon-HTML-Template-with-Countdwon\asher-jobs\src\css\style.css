/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header styles */
header {
    padding: 20px 0;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 20px;
}

/* Section styles */
section {
    padding: 60px 0;
}

.hero {
    text-align: center;
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero h2 {
    font-size: 3rem;
    margin-bottom: 20px;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Countdown styles */
#countdown {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 40px;
}

.countdown-item {
    text-align: center;
}

.countdown-number {
    font-size: 2.5rem;
    font-weight: bold;
    background: #000000;
    color: #ffffff;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    margin-bottom: 10px;
}

.countdown-label {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* About section */
.about {
    text-align: center;
    background: #ffffff;
}

.about h2 {
    margin-bottom: 30px;
}

.about p {
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
}

/* Contact section */
.contact {
    text-align: center;
    background: #f0f0f0;
}

.contact h2 {
    margin-bottom: 30px;
}

#newsletter-form {
    max-width: 500px;
    margin: 0 auto;
    display: flex;
    gap: 10px;
}

#newsletter-form input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #000000;
    border-radius: 4px;
    font-size: 1rem;
}

/* Responsive styles */
@media (max-width: 768px) {
    #countdown {
        flex-wrap: wrap;
    }
    
    #newsletter-form {
        flex-direction: column;
    }
    
    .hero h2 {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .countdown-number {
        width: 60px;
        height: 60px;
        font-size: 1.8rem;
    }
    
    .hero h2 {
        font-size: 2rem;
    }
}