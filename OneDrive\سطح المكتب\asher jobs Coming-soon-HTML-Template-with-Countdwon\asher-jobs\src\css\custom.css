/* Updated Color Scheme with Professional UI/UX Design Principles */
:root {
  /* Primary Colors - maintaining brand identity */
  --primary-dark: #003C43;     /* Dark teal - primary brand color */
  --primary-medium: #005F69;   /* Medium teal - secondary brand color */
  --primary-light: #77B0AA;    /* Light teal - accent color */
  
  /* Neutral Colors */
  --neutral-100: #F7F9FA;      /* Lightest background - replaces stark white */
  --neutral-200: #EDF2F4;      /* Light background for cards/sections */
  --neutral-300: #E2E8EB;      /* Borders, dividers */
  --neutral-400: #C5CDD3;      /* Disabled elements */
  --neutral-500: #8D98A4;      /* Subtle text, icons */
  --neutral-600: #5D6974;      /* Secondary text */
  --neutral-700: #2D3A45;      /* Primary text */
  --neutral-800: #1A242D;      /* Headings */
  
  /* Spacing and Typography */
  --border-radius: 8px;
  --transition: all 0.3s ease;
  --spacing-sm: 10px;
  --spacing-md: 20px;
  --spacing-lg: 30px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 20px;
  --font-size-xl: 28px;
  --font-size-xxl: 36px;
  
  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-dark), var(--primary-medium));
}

/* Base Elements */
html {
  font-size: 16px;
}

body {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--neutral-700);
  background-color: var(--neutral-100);
  font-family: 'Montserrat', sans-serif;
  margin: 0;
  padding: 0;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  min-height: 100vh; /* Ensure body takes full height */
  display: flex;
  flex-direction: column;
  visibility: visible !important; /* Force visibility */
}

h1, h2, h3, h4, h5, h6 {
  color: var(--neutral-800);
  font-weight: 700;
  margin: 0 0 var(--spacing-md) 0;
}

a {
  color: var(--primary-medium);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

/* Improved focus states for accessibility */
a:focus, button:focus, input:focus, textarea:focus, select:focus {
  outline: 3px solid rgba(0, 95, 105, 0.4);
  outline-offset: 2px;
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  box-sizing: border-box;
}

/* Header with Logo Styling */
.site-header {
    background-color: white;
    padding: 15px 0;
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-logo {
    display: flex;
    align-items: center;
}

.site-logo img {
    height: 100px;
    width: auto;
    transition: transform 0.3s ease;
}

.site-logo img:hover {
    transform: scale(1.05);
}

/* Footer Logo Styling */
.footer-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
}

.footer-logo img {
    height: 80px;
    width: auto;
    margin-bottom: 15px;
    transition: transform 0.3s ease;
}

.footer-logo img:hover {
    transform: scale(1.05);
}

.footer-logo h3 {
    margin: 10px 0 5px;
    color: #fff;
    font-size: 24px;
}

.footer-logo p {
    color: #ccc;
    font-size: 16px;
}

/* Responsive adjustments for header and footer */
@media (max-width: 768px) {
    .site-logo img {
        height: 80px;
    }
    
    .footer-logo img {
        height: 70px;
    }
}

@media (max-width: 480px) {
    .site-logo img {
        height: 70px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 10px;
    }
    
    .language-switcher {
        position: static;
        margin-top: 10px;
    }
    
    .footer-logo img {
        height: 60px;
    }
}

/* Hero Section */
.hero-section {
  background: var(--gradient-primary);
  color: white;
  padding: var(--spacing-lg) 0;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-section h1, .hero-section p {
  color: white;
}

.hero-section h1 {
  font-size: var(--font-size-xxl);
  margin-bottom: var(--spacing-md);
}

.hero-section p {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-lg);
}

.hero-cta {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

/* Button Styling */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-weight: 600;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  border: none;
  font-size: var(--font-size-md);
}

.btn-primary {
  background-color: #77B0AA;
  color: white;
}

.btn-primary:hover {
 background-color: #ffffff;
   transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: white;
  color: var(--primary-dark);
  border: 1px solid var(--primary-dark);
}

.btn-secondary:hover {
  background-color: var(--neutral-200);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Countdown Timer */
.countdown-container {
  margin-top: var(--spacing-lg);
}

.countdown-timer {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-sm);
  flex-wrap: wrap;
}

.timer-wrapper {
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  min-width: 80px;
  text-align: center;
  backdrop-filter: blur(5px);
}

.timer-wrapper .time {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: white;
}

.timer-wrapper .text {
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.8);
}

/* WhatsApp Section */
.wa, .whatsapp-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg) 0;
  text-align: center;
}

.wacontainer {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  text-align: center;
}

.title2 {
  color: var(--primary-dark);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-lg);
}

.image-container {
  margin: 0 auto;
  max-width: 200px;
  transition: var(--transition);
}

.image-container:hover {
  transform: scale(1.05);
}

.qr, .qr2, .qr3, .qr4, .qr5 {
  width: 200px;
  height: 200px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin: 0 auto;
}

.qr {
  background-image: url('../images/QR.png');
}

.qr2 {
  background-image: url('../images/qr2.png');
}

.qr3 {
  background-image: url('../images/qr3.png');
}

.qr4 {
  background-image: url('../images/qr4.png');
}

.qr5 {
  background-image: url('../images/qr5.png');
}

/* Article Section */
.comming-soon {
  width: 100%;
  padding: var(--spacing-lg) 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contact-section,
.contact-section.fade-in,
.contact-form-new2,
.contact-form-new2.fade-in,
.contact-form,
.contact-form-new,
.contact-form-new3,
.contact-form-new4 {
  width: 100%;
  max-width: 800px;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  text-align: center;
  box-sizing: border-box;
}

.contact-form {
  margin-bottom: var(--spacing-lg);
}

.contact-box {
  width: 100%;
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  box-sizing: border-box;
  text-align: center;
}

.title, .title2, .title3, .title4 {
  width: 100%;
  position: relative;
  padding-bottom: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--primary-light);
}

.article-content {
  width: 100%;
  max-width: 700px;
  margin: 0 auto;
  text-align: center;
}

.article-content p {
  margin-bottom: var(--spacing-md);
  text-align: center; /* Ensure paragraphs are centered */
}

.article-content strong {
  color: var(--neutral-800);
  font-weight: 600;
}

/* Center images within articles */
.pic {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: var(--spacing-md) 0;
}

.pic img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
  display: block;
}

/* Center content in all section types */
.contact-section,
.contact-section.fade-in,
.contact-form-new2,
.contact-form-new2.fade-in {
  width: 100%;
  max-width: 800px;
  margin: var(--spacing-lg) auto;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  text-align: center; /* Center all content */
}

.contact-box {
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  text-align: center; /* Center content in contact boxes */
}

/* Center titles */
.title {
  color: var(--primary-dark);
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-md);
  position: relative;
  padding-bottom: var(--spacing-sm);
  text-align: center; /* Center all titles */
}

.title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%; /* Center the underline */
  transform: translateX(-50%); /* Center the underline */
  width: 60px;
  height: 3px;
  background-color: var(--primary-light);
}

/* Ensure lists are centered properly */
.article-content ul {
  display: inline-block; /* Allow the list to be centered */
  text-align: left; /* Keep list items left-aligned for readability */
  margin: 0 auto var(--spacing-md); /* Center the list block */
}

/* Updated styling for contact-form-new2 to match width with contact-section */
.contact-form-new2,
.contact-form-new2.fade-in {
  width: 100%;
  max-width: 800px;
  margin: var(--spacing-lg) auto;
  background: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  text-align: center;
  box-sizing: border-box;
}

/* Ensure contact-section has the same width properties for consistency */
.contact-section,
.contact-section.fade-in {
  width: 100%;
  max-width: 800px;
  margin: var(--spacing-lg) auto;
}

/* Reset conflicting styles from customar.css */
.contact-box,
.contact-form,
.contact-form-new,
.contact-form-new2,
.contact-form-new3,
.contact-form-new4,
.contact-section {
  text-align: center !important;
  float: none !important;
  direction: inherit !important; /* Use the document's direction */
}

/* Center containers with flexbox - more reliable than transform */
.container {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

/* Main content containers */
.comming-soon,
.comming-soon.fade-in,
.contact-section,
.contact-section.fade-in,
.contact-form-new2,
.contact-form-new2.fade-in,
.contact-form,
.contact-form-new,
.contact-form-new3,
.contact-form-new4 {
  width: 100% !important;
  max-width: 800px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-sizing: border-box;
  /* Remove positioning that could conflict */
  position: static !important;
  left: auto !important;
  right: auto !important;
  transform: none !important;
}

/* Center content boxes */
.contact-box {
  width: 100%;
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  box-sizing: border-box;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .comming-soon,
  .comming-soon.fade-in,
  .contact-section,
  .contact-section.fade-in,
  .contact-form-new2,
  .contact-form-new2.fade-in,
  .contact-form,
  .contact-form-new,
  .contact-form-new3,
  .contact-form-new4 {
    max-width: 90% !important;
  }
}

@media (max-width: 768px) {
  .comming-soon,
  .comming-soon.fade-in,
  .contact-section,
  .contact-section.fade-in,
  .contact-form-new2,
  .contact-form-new2.fade-in,
  .contact-form,
  .contact-form-new,
  .contact-form-new3,
  .contact-form-new4 {
    max-width: 95% !important;
    padding: var(--spacing-md);
  }
  
  .contact-box {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .comming-soon,
  .comming-soon.fade-in,
  .contact-section,
  .contact-section.fade-in,
  .contact-form-new2,
  .contact-form-new2.fade-in,
  .contact-form,
  .contact-form-new,
  .contact-form-new3,
  .contact-form-new4 {
    max-width: 95% !important;
    padding: var(--spacing-sm);
    /* Keep the transform centering but adjust margins */
    margin-top: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }
  
  .contact-box {
    padding: var(--spacing-sm);
  }
}

/* Floating Sitemap */
.floating-sitemap {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

/* RTL adjustments for floating sitemap */
[dir="rtl"] .floating-sitemap {
  left: 20px;
  right: auto;
}

.sitemap-toggle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-dark);
  color: white;
  border: none;
  font-size: 24px;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
}

.sitemap-toggle:hover {
  background-color: var(--primary-medium);
  transform: scale(1.05);
}

.dropdown-menu {
  position: absolute;
  bottom: 60px;
  right: 0;
  width: 250px;
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-lg);
  display: none;
  text-align: center;
}

/* RTL adjustments for dropdown menu */
[dir="rtl"] .dropdown-menu {
  left: 0;
  right: auto;
}

.dropdown-menu.active {
  display: block;
}

.dropdown-menu h3 {
  color: var(--primary-dark);
  margin-bottom: var(--spacing-md);
}

.dropdown-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dropdown-menu li {
  margin-bottom: var(--spacing-sm);
}

.dropdown-menu a {
  color: var(--neutral-700);
  transition: var(--transition);
}

.dropdown-menu a:hover {
  color: var(--primary-dark);
}

/* Footer Styling - Updated to vertical layout */
footer {
  background-color: var(--primary-dark);
  color: white;
  padding: var(--spacing-lg) 0;
  text-align: center;
  border-top: 3px solid var(--primary-light);
  margin-top: 50px;
}

.footer-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 30px;
  width: 100%;
}

.footer-logo img {
  height: 80px;
  width: auto;
  margin-bottom: 15px;
  transition: transform 0.3s ease;
}

.footer-logo img:hover {
  transform: scale(1.05);
}

.footer-logo h3 {
  margin: 10px 0 5px;
  color: #fff;
  font-size: 24px;
  font-weight: 600;
}

.footer-logo p {
  color: #ccc;
  font-size: 16px;
  margin-top: 5px;
}

.footer-links {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 30px;
  width: 100%;
}

.footer-links a {
  color: var(--primary-light);
  margin-bottom: 10px;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: white;
  text-decoration: underline;
}

.footer-social {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 30px;
  width: 100%;
}

.footer-social p {
  margin-bottom: 15px;
  color: #fff;
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons a {
  color: var(--primary-light);
  font-size: 20px;
  transition: color 0.3s ease;
}

.social-icons a:hover {
  color: white;
}

.footer-bottom {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  width: 100%;
}

.footer-bottom p {
  color: #ccc;
  font-size: 14px;
}

/* Responsive footer adjustments */
@media (max-width: 480px) {
  .footer-logo img {
    height: 60px;
  }
  
  .footer-logo h3 {
    font-size: 20px;
  }
  
  .footer-content {
    padding: 0 10px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section h1 {
    font-size: var(--font-size-xl);
  }
  
  .hero-section p {
    font-size: var(--font-size-md);
  }
  
  .hero-cta {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .timer-wrapper {
    min-width: 60px;
    padding: var(--spacing-sm);
  }
  
  .timer-wrapper .time {
    font-size: var(--font-size-lg);
  }
  
  .timer-wrapper .text {
    font-size: 12px;
  }
  
  .wacontainer {
    padding: var(--spacing-sm);
  }
  
  .qr, .qr2, .qr3, .qr4, .qr5 {
    width: 300px;
    height: 150px;
  }
}

@media (max-width: 480px) {
  .countdown-timer {
    gap: var(--spacing-sm);
  }
  
  .timer-wrapper {
    min-width: 50px;
    padding: 8px;
  }
  
  .timer-wrapper .time {
    font-size: var(--font-size-md);
  }
  
  .timer-wrapper .text {
    font-size: 10px;
  }
  
  .floating-sitemap {
    bottom: 10px;
    right: 10px;
  }

  [dir="rtl"] .floating-sitemap {
    left: 10px;
    right: auto;
  }
  
  .sitemap-toggle {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .dropdown-menu {
    width: 200px;
    bottom: 50px;
  }
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

.spinner-border {
  width: 3rem;
  height: 3rem;
  border: 0.25rem solid var(--primary-light);
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
  to { transform: rotate(360deg); }
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* Search Bar */
.search-bar {
  display: flex;
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.search-input-container {
  flex: 1;
  min-width: 200px;
}

.search-input-container input {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--neutral-300);
  border-radius: var(--border-radius);
}

.search-button-container {
  margin-left: var(--spacing-sm);
}

.search-button-container button {
  padding: 10px 20px;
  background-color: var(--primary-dark);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.search-button-container button:hover {
  background-color: var(--primary-medium);
}

@media (max-width: 480px) {
  .search-bar {
    flex-direction: column;
  }
  
  .search-button-container {
    margin-left: 0;
    margin-top: var(--spacing-sm);
  }
}

/* Fix fade-in animation issues */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Fallback to ensure content is visible even if JS fails */
@media (prefers-reduced-motion: reduce) {
  .fade-in {
    opacity: 1;
    transform: none;
    transition: none;
  }
}

/* Add a timeout to ensure content becomes visible regardless of JS */
@keyframes forceVisible {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: forceVisible 0s 2s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Language Switcher Styles */
.language-switcher {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000; /* High z-index but not too high to block other UI */
  pointer-events: auto; /* Ensure it's clickable */
}

/* RTL adjustments for language switcher */
[dir="rtl"] .language-switcher {
  left: 20px;
  right: auto;
}

.lang-switch-btn {
  display: flex;
  align-items: center;
  background-color: var(--primary-dark);
  color: white;
  padding: 8px 16px;
  border-radius: 30px;
  text-decoration: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  font-family: 'Montserrat', 'Cairo', sans-serif;
  font-weight: 600;
  font-size: 14px;
}

.lang-switch-btn:hover, .lang-switch-btn:focus {
  background-color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.25);
}

.lang-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  animation: rotate 8s linear infinite;
}

.lang-text {
  position: relative;
  overflow: hidden;
}

.lang-text::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: white;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.lang-switch-btn:hover .lang-text::after {
  transform: translateX(0);
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .language-switcher {
    top: 10px;
    right: 10px;
  }

  [dir="rtl"] .language-switcher {
    left: 10px;
    right: auto;
  }

  .lang-switch-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .lang-icon svg {
    width: 18px;
    height: 18px;
  }
}

/* Ensure body and main containers are visible */
.container, 
.comming-soon,
.contact-section,
.contact-form,
.contact-box,
footer {
  visibility: visible !important;
  display: block !important;
  opacity: 1 !important;
}

/* AI Job Analysis Section Styling */
.ai-analysis-section {
  padding: 60px 0;
  background-color: #f9f9f9;
}

.ai-analysis-section .container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 20px;
}

.ai-analysis-section .section-title {
  font-size: 32px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 15px;
  text-align: center;
}

.ai-analysis-section .section-description {
  font-size: 16px;
  color: #555555;
  text-align: center;
  margin-bottom: 30px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.analysis-container {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.input-container {
  margin-bottom: 20px;
  position: relative;
}

#jobDescriptionInput {
  width: 100%;
  min-height: 150px;
  padding: 15px;
  border: 1px solid #dddddd;
  border-radius: 5px;
  font-size: 16px;
  resize: vertical;
  font-family: inherit;
  margin-bottom: 15px;
}

.analyze-btn {
  background-color: #000000;
  color: #ffffff;
  padding: 12px 25px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.analyze-btn:hover {
  background-color: #333333;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #000000;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.results-container, .error-container {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eeeeee;
}

.results-container h3, .error-container h3 {
  font-size: 22px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 15px;
}

.results-content {
  background-color: #f5f5f5;
  border-radius: 5px;
  padding: 20px;
  font-size: 15px;
  line-height: 1.6;
  color: #333333;
}

.results-content h4 {
  font-size: 18px;
  font-weight: 600;
  margin-top: 15px;
  margin-bottom: 10px;
  color: #000000;
}

.results-content ul {
  padding-left: 20px;
  margin-bottom: 15px;
}

.results-content li {
  margin-bottom: 5px;
}

.error-container {
  text-align: center;
  color: #dc3545;
}

.error-container p {
  margin-bottom: 15px;
}

.retry-btn {
  background-color: #dc3545;
  color: #ffffff;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.retry-btn:hover {
  background-color: #c82333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ai-analysis-section {
    padding: 40px 0;
  }
  
  .ai-analysis-section .section-title {
    font-size: 28px;
  }
  
  .analysis-container {
    padding: 20px;
  }
  
  #jobDescriptionInput {
    min-height: 120px;
  }
  
  .analyze-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .ai-analysis-section .section-title {
    font-size: 24px;
  }
  
  .ai-analysis-section .section-description {
    font-size: 14px;
  }
  
  .analysis-container {
    padding: 15px;
  }
  
  .results-content {
    padding: 15px;
    font-size: 14px;
  }
}

/* Partners Grid Section - Modern Professional Design */
.partners-grid-section {
    padding: 60px 0;
    background-color: var(--neutral-100);
    text-align: center;
    width: 100%;
    position: relative;
}

.partners-grid-section .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.partners-grid-section .section-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: 40px;
    position: relative;
    padding-bottom: 15px;
    text-align: center;
    color: var(--neutral-800);
}

.partners-grid-section .section-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-medium);
    transition: width 0.3s ease;
}

.partners-grid-section .section-title:hover:after {
    width: 80px;
}

/* Partners Grid Layout */
.partners-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    max-width: 1100px;
    margin: 0 auto;
}

.partner-grid-item {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    aspect-ratio: 4/3;
    overflow: hidden;
}

.partner-grid-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.partner-grid-item a {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 20px;
    box-sizing: border-box;
}

.partner-logo-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.partner-grid-logo {
    max-width: 85%;
    max-height: 85%;
    object-fit: contain;
    transition: all 0.3s ease;
    filter: grayscale(30%) opacity(0.9);
}

.partner-grid-item:hover .partner-grid-logo {
    filter: grayscale(0%) opacity(1);
    transform: scale(1.05);
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .partners-grid {
        gap: 25px;
    }
}

@media (max-width: 992px) {
    .partners-grid-section {
        padding: 50px 0;
    }
    
    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .partners-grid-section .section-title {
        font-size: 28px;
        margin-bottom: 30px;
    }
}

@media (max-width: 576px) {
    .partners-grid-section {
        padding: 40px 0;
    }
    
    .partners-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        max-width: 280px;
    }
    
    .partners-grid-section .section-title {
        font-size: 24px;
        margin-bottom: 25px;
    }
    
    .partner-grid-item {
        aspect-ratio: 3/2;
    }
}

/* Global centering styles */
body {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  overflow-x: hidden;
}

/* Center all containers */
.container {
  width: 100%;
  max-width: 1200px;
  margin-left: auto !important;
  margin-right: auto !important;
  padding: 0 var(--spacing-md);
  box-sizing: border-box;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

/* Center all sections */
section, 
.section, 
.hero-section, 
.whatsapp-section, 
.partners-section, 
.job-listings-section, 
.ai-analysis-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* Center all headings */
h1, h2, h3, h4, h5, h6, 
.section-title, 
.title, 
.title2, 
.title3, 
.title4 {
  text-align: center;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

/* Center all paragraphs */
p {
  text-align: center;
  width: 100%;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Center all content blocks */
.hero-content, 
.article-content, 
.wacontainer, 
.hidden-content, 
.comming-soon, 
.contact-section, 
.contact-form, 
.contact-form-new, 
.contact-form-new2, 
.contact-form-new3, 
.contact-form-new4 {
  width: 100% !important;
  max-width: 800px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
  /* Remove positioning that could conflict */
  position: static !important;
  left: auto !important;
  right: auto !important;
  transform: none !important;
}

/* Center all buttons and CTAs */
.btn, 
.hero-cta, 
.read-more-btn, 
.analyze-btn, 
button {
  margin-left: auto !important;
  margin-right: auto !important;
  text-align: center !important;
}

/* Center all images and media */
img, 
.pic, 
.image-container, 
.video-container, 
.qr, 
.qr2, 
.qr3, 
.qr4, 
.qr5 {
  margin-left: auto !important;
  margin-right: auto !important;
  display: block;
}

/* Center all lists while maintaining text alignment */
ul, ol {
  display: inline-block;
  margin-left: auto;
  margin-right: auto;
  text-align: left; /* Keep list items left-aligned for readability */
}

/* Center all boxes and cards */
.contact-box, 
.job-card, 
.partner-item, 
.analysis-container {
  width: 100%;
  max-width: 800px;
  margin-left: auto !important;
  margin-right: auto !important;
  text-align: center !important;
}

/* Center header content */
.site-header {
  width: 100%;
  text-align: center;
}

.header-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.site-logo {
  display: flex;
  justify-content: center;
}

/* Center footer content */
footer {
  width: 100%;
  text-align: center;
}

.footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

/* Fix for title underlines */
.title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--primary-light);
}

/* Ensure all content is visible */
.container, 
.comming-soon,
.contact-section,
.contact-form,
.contact-box,
footer,
section,
.hero-content,
.article-content {
  visibility: visible !important;
  display: block !important;
  opacity: 1 !important;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .comming-soon,
  .contact-section,
  .contact-form-new2,
  .contact-form,
  .contact-form-new,
  .contact-form-new3,
  .contact-form-new4,
  .hero-content,
  .article-content,
  .wacontainer {
    max-width: 90% !important;
  }
}

@media (max-width: 768px) {
  .comming-soon,
  .contact-section,
  .contact-form-new2,
  .contact-form,
  .contact-form-new,
  .contact-form-new3,
  .contact-form-new4,
  .hero-content,
  .article-content,
  .wacontainer {
    max-width: 95% !important;
    padding: var(--spacing-md);
  }
  
  .header-content {
    flex-direction: column;
    align-items: center;
  }
  
  .footer-content {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .comming-soon,
  .contact-section,
  .contact-form-new2,
  .contact-form,
  .contact-form-new,
  .contact-form-new3,
  .contact-form-new4,
  .hero-content,
  .article-content,
  .wacontainer {
    max-width: 95% !important;
    padding: var(--spacing-sm);
  }
  
  .container,
  .hero-content,
  .wacontainer,
  .article-content,
  .hidden-content,
  .contact-box {
    padding-left: 10px;
    padding-right: 10px;
  }
}
