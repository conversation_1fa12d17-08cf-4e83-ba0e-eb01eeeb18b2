/* 
 * RTL-specific styles for Arabic version
 * This file extends the base styles from custom.css with RTL adjustments
 * Updated to center content while maintaining RTL text alignment
 */

/* Base RTL settings */
body {
    direction: rtl;
    text-align: right; /* Keep text alignment RTL */
    font-family: 'Cairo', 'Montserrat', sans-serif;
}

/* Center all containers while maintaining RTL */
.container {
    margin-left: auto !important;
    margin-right: auto !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important; /* Center children horizontally */
}

/* Header RTL adjustments with centering */
.site-header {
    text-align: center; /* Center the header content */
    width: 100%;
}

.header-content {
    flex-direction: row-reverse;
    justify-content: center; /* Center header content */
    align-items: center;
    width: 100%;
}

.site-logo {
    display: flex;
    justify-content: center;
}

/* Language switcher RTL positioning */
.language-switcher {
    position: fixed;
    top: 20px;
    left: 20px;
    right: auto;
    z-index: 1000;
    pointer-events: auto;
}

/* Ensure all content is visible */
.container, 
.comming-soon,
.contact-section,
.contact-form,
.contact-box,
footer {
    visibility: visible !important;
    display: block !important;
    opacity: 1 !important;
}



.hero-content {
    display: flex;
    flex-direction: column;
    align-items: center; /* Center children horizontally */
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

.hero-content h1, 
.hero-content p {
    text-align: center; /* Center text */
    width: 100%;
}



/* Floating sitemap RTL positioning */
.floating-sitemap {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: auto;
    z-index: 1000;
}

.dropdown-menu {
    left: 0;
    right: auto;
    text-align: right; /* Keep menu items RTL */
}

.dropdown-menu ul {
    padding-right: 20px;
    padding-left: 0;
}

/* WhatsApp section RTL adjustments with centering */
.whatsapp-section {
    text-align: center; /* Center the section content */
    width: 100%;
}

.whatsapp-section h2 {
    text-align: center;
    width: 100%;
}

.wacontainer {
    text-align: center; /* Center the container content */
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

.title2, .title3, .title4 {
    text-align: center; /* Center titles */
    width: 100%;
}

.image-container {
    display: flex;
    justify-content: center; /* Center images */
    width: 100%;
}

/* Article content RTL adjustments with centering */
.article-content {
    text-align: center; /* Center the article content */
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
}

.article-content p {
    text-align: center; /* Center paragraphs */
    width: 100%;
}

.hidden-content {
    text-align: center; /* Center hidden content */
    width: 100%;
}

.hidden-content p {
    text-align: center; /* Center paragraphs in hidden content */
    width: 100%;
}

.hidden-content ul {
    display: inline-block; /* Allow the list to be centered */
    text-align: right; /* Keep list items RTL for readability */
    margin: 0 auto var(--spacing-md); /* Center the list block */
    padding-right: 20px;
    padding-left: 0;
}

/* Read more button centering */
.read-more-btn {
    margin: 0 auto; /* Center the button */
    display: block;
    text-align: center;
}

/* Title underline centering */
.title {
    text-align: center; /* Center titles */
    width: 100%;
    position: relative;
    padding-bottom: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.title:after {
    right: 50%;
    left: auto;
    transform: translateX(50%); /* Center the underline */
}

/* Video container centering */
.video-container {
    text-align: center; /* Center video container */
    width: 100%;
    display: flex;
    justify-content: center;
}

/* Partners Grid Section RTL adjustments */
.partners-grid-section {
    direction: rtl;
    text-align: center;
    padding: 60px 0;
    background-color: var(--neutral-100);
    width: 100%;
    position: relative;
}

.partners-grid-section .section-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: 40px;
    position: relative;
    padding-bottom: 15px;
    text-align: center;
    color: var(--neutral-800);
}

.partners-grid-section .section-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 50%;
    left: auto;
    transform: translateX(50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-medium);
    transition: width 0.3s ease;
}

.partners-grid-section .section-title:hover:after {
    width: 80px;
}

/* Partners Grid Layout - RTL adjustments */
.partners-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    max-width: 1100px;
    margin: 0 auto;
}

/* Responsive Adjustments for RTL */
@media (max-width: 992px) {
    .partners-grid-section {
        padding: 50px 0;
    }
    
    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .partners-grid-section .section-title {
        font-size: 28px;
        margin-bottom: 30px;
    }
}

@media (max-width: 576px) {
    .partners-grid-section {
        padding: 40px 0;
    }
    
    .partners-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        max-width: 280px;
    }
    
    .partners-grid-section .section-title {
        font-size: 24px;
        margin-bottom: 25px;
    }
}

/* Job listings RTL adjustments with centering */
.job-listings-section {
    direction: rtl;
    text-align: center; /* Center the section content */
    width: 100%;
}

.job-listings-section .section-title {
    text-align: center;
    width: 100%;
}

.job-listings-section .section-title:after {
    right: 50%;
    left: auto;
    transform: translateX(50%);
}

.job-card {
    text-align: center; /* Center job card content */
    margin-left: auto;
    margin-right: auto;
}

.job-card-header {
    flex-direction: row-reverse;
    justify-content: center;
}

.job-type {
    margin-right: 0;
    margin-left: 15px;
}

.company-info {
    text-align: center; /* Center company info */
}

.job-location {
    flex-direction: row-reverse;
    justify-content: center;
}

.job-location i {
    margin-right: 0;
    margin-left: 5px;
}

.job-card-footer {
    justify-content: center; /* Center footer content */
}

/* AI analysis section RTL adjustments with centering */
.ai-analysis-section {
    direction: rtl;
    text-align: center; /* Center the section content */
    width: 100%;
}

.ai-analysis-section .section-title,
.ai-analysis-section .section-description {
    text-align: center;
    width: 100%;
}

.analysis-container {
    margin-left: auto;
    margin-right: auto;
}

.results-content {
    text-align: center;
}

.results-content ul {
    display: inline-block;
    text-align: right;
    padding-right: 20px;
    padding-left: 0;
    margin: 0 auto;
}

/* Footer RTL adjustments with vertical layout */
footer {
    text-align: center;
    width: 100%;
}

.footer-content {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.footer-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 30px;
    width: 100%;
}

.footer-links {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
    width: 100%;
}

.footer-social {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
    width: 100%;
}

.social-icons {
    flex-direction: row;
    justify-content: center;
}

/* Fix for image paths in background elements */
.qr {
    background-image: url('../images/qr.png');
    margin: 0 auto;
}

.qr2 {
    background-image: url('../images/qr2.png');
    margin: 0 auto;
}

.qr3 {
    background-image: url('../images/qr3.png');
    margin: 0 auto;
}

.qr4 {
    background-image: url('../images/qr4.png');
    margin: 0 auto;
}

.qr5 {
    background-image: url('../images/qr5.png');
    margin: 0 auto;
}

/* Center all section types */
.contact-section,
.contact-section.fade-in,
.contact-form-new2,
.contact-form-new2.fade-in,
.contact-form,
.contact-form-new,
.contact-form-new3,
.contact-form-new4 {
    width: 100% !important;
    max-width: 800px !important;
    margin-left: auto !important;
    margin-right: auto !important;
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-sizing: border-box;
    /* Remove positioning that could conflict */
    position: static !important;
    left: auto !important;
    right: auto !important;
    transform: none !important;
    text-align: center !important;
}

/* Center content boxes */
.contact-box {
    width: 100%;
    background-color: white;
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    box-sizing: border-box;
    margin-left: auto !important;
    margin-right: auto !important;
    text-align: center !important;
}

/* Main content containers */
.comming-soon,
.comming-soon.fade-in {
    width: 100% !important;
    max-width: 800px !important;
    margin-left: auto !important;
    margin-right: auto !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
}

/* Responsive adjustments for RTL */
@media (max-width: 992px) {
    .partner-item {
        width: 180px;
        height: 140px;
    }
    
    .partner-item img {
        max-height: 100px;
    }
}

@media (max-width: 768px) {
    .partner-item {
        width: 160px;
        height: 130px;
        margin: 0 10px;
    }
    
    .partner-item img {
        max-height: 90px;
    }
}

@media (max-width: 480px) {
    .partner-item {
        width: 140px;
        height: 120px;
    }
    
    .partner-item img {
        max-height: 80px;
    }
}

@media (max-width: 768px) {
    .language-switcher {
        position: fixed;
        top: 10px;
        left: 10px;
        right: auto;
        z-index: 1000;
    }

    .floating-sitemap {
        position: fixed;
        bottom: 10px;
        left: 10px;
        right: auto;
        z-index: 1000;
    }
    
    .job-card-header {
        flex-direction: column;
        align-items: center; /* Center in mobile view */
    }
    
    .job-type {
        align-self: center; /* Center in mobile view */
        margin-bottom: 10px;
    }
    
    .hero-content h1, 
    .hero-content p,
    .article-content p,
    .hidden-content p {
        text-align: center; /* Ensure text stays centered on mobile */
    }
}

@media (max-width: 480px) {
    .header-content {
        flex-direction: column;
        align-items: center;
    }
    
    .language-switcher {
        position: fixed;
        top: 10px;
        left: 10px;
        right: auto;
        z-index: 1000;
        margin-top: 0;
        margin-bottom: 0;
    }
    
    .footer-content {
        flex-direction: column;
        align-items: center;
    }
    
    .footer-links, .footer-social {
        text-align: center;
    }
    
    /* Ensure all content stays centered on small screens */
    .container,
    .hero-content,
    .wacontainer,
    .article-content,
    .hidden-content,
    .contact-box {
        padding-left: 10px;
        padding-right: 10px;
    }
}

/* Fix for fade-in animations in RTL context */
.fade-in {
    transform: translateY(20px);
}

.fade-in.visible {
    transform: translateY(0);
}
