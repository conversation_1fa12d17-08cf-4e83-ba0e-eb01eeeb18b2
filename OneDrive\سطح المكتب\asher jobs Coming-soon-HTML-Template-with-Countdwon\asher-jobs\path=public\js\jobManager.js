// Add proper async initialization
const jobManager = {
  initializeSampleJobs: async () => {
    try {
      if (!localStorage.getItem('jobs')) {
        const response = await fetch('/api/jobs/init', { method: 'POST' });
        if (!response.ok) throw new Error('Init failed');
        const jobs = await response.json();
        localStorage.setItem('jobs', JSON.stringify(jobs));
      }
    } catch (error) {
      console.error('Initialization error:', error);
    }
  },

  // Update response handling
  handleResponse: async (response) => {
    const contentType = response.headers.get('content-type');
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText.slice(0, 100)}`);
    }

    if (!contentType?.includes('application/json')) {
      throw new Error(`Invalid content type: ${contentType}`);
    }

    return response.json();
  }
};

// Initialize after DOM load
document.addEventListener('DOMContentLoaded', async () => {
  await jobManager.initializeSampleJobs();
}); 