<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Asher Jobs connects candidates with suitable job opportunities and provides training for recruiters to enhance hiring processes.">
    <meta name="keywords" content="jobs, recruitment, career opportunities, job training, Asher Jobs, employee matching, recruiter training, professional growth">
    <meta name="author" content="Asher Jobs - وظائف العاشر">
    <meta name="geo.region" content="EG">
    <meta name="geo.placename" content="10th of Ramadan City">
    <meta name="geo.position" content="30.2967;31.7433">
    <meta name="ICBM" content="30.2967, 31.7433">
    

     <!-- Favicon -->
    <link rel="icon" href="../images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../images/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="../images/favicon.ico">
    <meta name="theme-color" content="#003C43">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Main custom css -->
    <link href="../css/custom.css" rel="stylesheet" media="screen">
    <link href="../css/custom-ar.css" rel="stylesheet" media="screen">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- Google Ads -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2136749359648243" crossorigin="anonymous"></script>
    <script async custom-element="amp-auto-ads" src="https://cdn.ampproject.org/v0/amp-auto-ads-0.1.js"></script>
    
    <!-- Page Title -->
    <title>Asher Jobs وظائف العاشر</title>
    
    <!-- Favicon -->
    <link rel="icon" href="src/images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="src/images/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="src/images/favicon.ico">
    <meta name="theme-color" content="#003C43">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">

    
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- Main CSS - Order matters: base styles first -->
    <link href="src/css/style.css" rel="stylesheet" media="screen">
    <link href="src/css/custom.css" rel="stylesheet" media="screen">
    
    <!-- Google Ads -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2136749359648243" crossorigin="anonymous"></script>
    <script async custom-element="amp-auto-ads" src="https://cdn.ampproject.org/v0/amp-auto-ads-0.1.js"></script>
    
    <style>
        /* Contact Form Specific Styles */
        .contact-form-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-control.error {
            border-color: #dc3545;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: none;
        }
        
        /* Submit button styling */
        .submit-btn {
            background-color: var(--primary-color, #003C43);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            max-width: 300px;
            margin: 1rem auto;
            position: relative;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .submit-btn:hover {
            background-color: var(--primary-dark, #002a30);
        }
        
        .submit-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .success-message {
            display: none;
            background-color: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .contact-info-section {
            margin-top: 3rem;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        
        .contact-info-item {
            flex: 1 0 30%;
            min-width: 250px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .contact-info-item i {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .contact-info-item h3 {
            margin-bottom: 0.5rem;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .contact-form-container {
                padding: 1.5rem;
            }
            
            .contact-info-item {
                flex: 1 0 100%;
            }
            
            .submit-btn {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Header with Logo -->
    <header class="site-header">
        <div class="container">
            <div class="header-content">
                <div class="site-logo">
                    <a href="../../index.html">
                        <img src="../images/logo.png" alt="Asher Jobs Logo" height="100">
                    </a>
                </div>
                
                <!-- Language Switcher -->
                <div class="language-switcher">
                    <a href="contactar.html" class="lang-switch-btn">
                        <span class="lang-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path fill="none" d="M0 0h24v24H0z"/>
                                <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-2.29-2.333A17.9 17.9 0 0 1 8.027 13H4.062a8.008 8.008 0 0 0 5.648 6.667zM10.03 13c.151 2.439.848 4.73 1.97 6.752A15.905 15.905 0 0 0 13.97 13h-3.94zm9.908 0h-3.965a17.9 17.9 0 0 1-1.683 6.667A8.008 8.008 0 0 0 19.938 13zM4.062 11h3.965A17.9 17.9 0 0 1 9.71 4.333 8.008 8.008 0 0 0 4.062 11zm5.969 0h3.938A15.905 15.905 0 0 0 12 4.248 15.905 15.905 0 0 0 10.03 11zm4.259-6.667A17.9 17.9 0 0 1 15.938 11h3.965a8.008 8.008 0 0 0-5.648-6.667z"/>
                            </svg>
                        </span>
                        <span class="lang-text">العربية</span>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="main-nav">
        <div class="container">
            <ul class="nav-links">
                <li><a href="../../index.html">Home</a></li>
                <li><a href="jobs.html">Jobs</a></li>
                <li><a href="contact.html" class="active">Contact Us</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <div class="container">
            <div class="page-header">
                <h1>Contact Us</h1>
                <p>Have questions or need assistance? We're here to help. Fill out the form below and we'll get back to you as soon as possible.</p>
            </div>
            
            <div class="contact-form-container">
                <div id="successMessage" class="success-message">
                    Thank you for your message! We will get back to you soon.
                </div>
                
                <form id="contactForm">
                    <!-- CSRF Token (hidden) -->
                    <input type="hidden" name="_csrf" id="csrfToken" value="">
                    
                    <div class="form-group">
                        <label for="fullName">Full Name *</label>
                        <input type="text" id="fullName" name="fullName" class="form-control" required>
                        <div class="error-message" id="fullNameError">Please enter your full name</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email Address *</label>
                        <input type="email" id="email" name="email" class="form-control" required>
                        <div class="error-message" id="emailError">Please enter a valid email address</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Phone Number *</label>
                        <input type="tel" id="phone" name="phone" class="form-control" required placeholder="e.g., +20 ************">
                        <div class="error-message" id="phoneError">Please enter a valid phone number</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="subject">Subject *</label>
                        <select id="subject" name="subject" class="form-control" required>
                            <option value="">Select a subject</option>
                            <option value="Job Application">Job Application</option>
                            <option value="Business Inquiry">Business Inquiry</option>
                            <option value="Technical Support">Technical Support</option>
                            <option value="Feedback">Feedback</option>
                            <option value="Other">Other</option>
                        </select>
                        <div class="error-message" id="subjectError">Please select a subject</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="message">Message *</label>
                        <textarea id="message" name="message" class="form-control" rows="5" required maxlength="1000"></textarea>
                        <div class="error-message" id="messageError">Please enter your message</div>
                        <small id="charCount">0/1000 characters</small>
                    </div>
                    
                    <button type="submit" class="submit-btn" id="submitBtn">
                        Send Message
                        <span class="spinner" id="submitSpinner"></span>
                    </button>
                </form>
            </div>
            
            <div class="contact-info-section">
                <div class="contact-info-item">
                    <i class="fa fa-envelope"></i>
                    <h3>Email</h3>
                    <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
                
                <div class="contact-info-item">
                    <i class="fa fa-phone"></i>
                    <h3>Phone</h3>
                    <p><a href="tel:+20156454666">+20 156 454 666</a></p>
                </div>
                
                <div class="contact-info-item">
                    <i class="fa fa-map-marker"></i>
                    <h3>Address</h3>
                    <p>10th of Ramadan City, Cairo, Egypt</p>
                </div>
            </div>
        </div>
    </main>
    <!-- Footer: Modern, Accessible, and Responsive (English) -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-brand">
                <a href="../../index.html" class="footer-logo" aria-label="Asher Jobs Home">
                    <img src="../images/logo.png" alt="Asher Jobs Logo" height="60" loading="lazy">
                    <h3>Asher Jobs</h3>
                </a>
               
                <p class="footer-tagline">Connecting Talent with Opportunity</p>
            </div>
            <nav class="footer-nav" aria-label="Footer Navigation">
                <ul class="footer-links">
                    <li><a href="../html/jobs.html">Browse Jobs</a></li>
                    <li><a href="../html/adminLogin.html">Admin Login</a></li>
                    <li><a href="../html/contact.html">Contact Us</a></li>
                    <li><a href="../html/privacy.html">Privacy Policy</a></li>
                </ul>
            </nav>
            <div class="footer-social">
              
                <div class="social-icons">
                    <a href="https://www.facebook.com/profile.php?id=61566379346391" aria-label="Facebook" class="fa fa-facebook" tabindex="0"></a>
                    <a href="https://www.linkedin.com/company/3ashr-jobs/?viewAsMember=true" aria-label="LinkedIn" class="fa fa-linkedin" tabindex="0"></a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 Asher Jobs. All rights reserved.</p>
        </div>
  <style>
    /* Basic Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.site-footer {
  background-color: #003C43;
  color: #f1f1f1;
  padding: 40px 20px 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2rem;
}

.footer-brand {
  flex: 1 1 250px;
}

.footer-brand h3 {
  font-size: 1.5rem;
  margin-top: 10px;
  color: #ffffff;
}

.footer-tagline {
  font-size: 0.95rem;
  color: #cfcfcf;
  margin-top: 5px;
}

.footer-logo img {
  max-width: 100%;
  height: 100px;
  border-radius: 5px;
}

.footer-nav {
  flex: 1 1 200px;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-top: 20px;
}

.footer-links a {
  color: #cfcfcf;
  display: inline-block;
  transition: transform 0.3s ease;
}

.footer-links a:hover,
.footer-links a:focus {
  color: #77B0AA;
  text-decoration: none;
    transform: scale(1.1);
}

.footer-social {
  flex: 1 1 200px;
  margin-top: 70px;
}

.footer-social p {
  font-weight: bold;
  margin-bottom: 10px;
}

.social-icons a {
  display: inline-block;
  font-size: 1.25rem;
  margin-right: 15px;
  color: #cfcfcf;
  transition: color 0.3s, transform 0.3s;
}

.social-icons a:hover,
.social-icons a:focus {
  color: #ffffff;
  transform: scale(1.1);
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  font-size: 0.875rem;
  color: #999;
  border-top: 1px solid #444;
  margin-top: 30px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .footer-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .footer-social, .footer-brand, .footer-nav {
    flex: 1 1 100%;
  }

  .social-icons a {
    margin: 0 10px;
  }
}

  </style>

    <!-- Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Generate CSRF token
            const csrfToken = generateCSRFToken();
            document.getElementById('csrfToken').value = csrfToken;
            
            // Form elements
            const contactForm = document.getElementById('contactForm');
            const fullNameInput = document.getElementById('fullName');
            const emailInput = document.getElementById('email');
            const phoneInput = document.getElementById('phone');
            const subjectSelect = document.getElementById('subject');
            const messageTextarea = document.getElementById('message');
            const submitBtn = document.getElementById('submitBtn');
            const submitSpinner = document.getElementById('submitSpinner');
            const successMessage = document.getElementById('successMessage');
            const charCount = document.getElementById('charCount');
            
            // Error message elements
            const fullNameError = document.getElementById('fullNameError');
            const emailError = document.getElementById('emailError');
            const phoneError = document.getElementById('phoneError');
            const subjectError = document.getElementById('subjectError');
            const messageError = document.getElementById('messageError');
            
            // Character counter for message
            messageTextarea.addEventListener('input', function() {
                const currentLength = this.value.length;
                charCount.textContent = `${currentLength}/1000 characters`;
            });
            
            // Form validation
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Reset error states
                resetErrors();
                
                // Validate form
                let isValid = true;
                
                // Validate full name
                if (!fullNameInput.value.trim()) {
                    showError(fullNameInput, fullNameError);
                    isValid = false;
                }
                
                // Validate email
                if (!isValidEmail(emailInput.value)) {
                    showError(emailInput, emailError);
                    isValid = false;
                }
                
                // Validate phone
                if (!isValidPhone(phoneInput.value)) {
                    showError(phoneInput, phoneError);
                    isValid = false;
                }
                
                // Validate subject
                if (!subjectSelect.value) {
                    showError(subjectSelect, subjectError);
                    isValid = false;
                }
                
                // Validate message
                if (!messageTextarea.value.trim()) {
                    showError(messageTextarea, messageError);
                    isValid = false;
                }
                
                // If form is valid, submit it
                if (isValid) {
                    submitForm();
                }
            });
            
            // Helper functions
            function resetErrors() {
                const errorElements = document.querySelectorAll('.error-message');
                const formControls = document.querySelectorAll('.form-control');
                
                errorElements.forEach(el => el.style.display = 'none');
                formControls.forEach(el => el.classList.remove('error'));
            }
            
            function showError(inputElement, errorElement) {
                inputElement.classList.add('error');
                errorElement.style.display = 'block';
            }
            
            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }
            
            function isValidPhone(phone) {
                // Allow various phone formats with optional country code
                const phoneRegex = /^(\+\d{1,3}\s?)?(\(\d{1,4}\)\s?)?[\d\s-]{7,15}$/;
                return phoneRegex.test(phone);
            }
            
            function generateCSRFToken() {
                // Generate a random token (in a real app, this would come from the server)
                return Math.random().toString(36).substring(2, 15) + 
                       Math.random().toString(36).substring(2, 15);
            }
            
            function submitForm() {
                // Show loading state
                submitBtn.disabled = true;
                submitSpinner.style.display = 'inline-block';
                
                // Prepare form data
                const formData = {
                    name: fullNameInput.value,
                    email: emailInput.value,
                    phone: phoneInput.value,
                    subject: subjectSelect.value,
                    message: messageTextarea.value,
                    _captcha: false,
                    _template: 'table'
                };
                
                // Send form data using FormSubmit service
                fetch('https://formsubmit.co/<EMAIL>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Show success message
                    successMessage.style.display = 'block';
                    
                    // Reset form
                    contactForm.reset();
                    charCount.textContent = '0/1000 characters';
                    
                    // Scroll to success message
                    successMessage.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    
                    // Hide success message after 5 seconds
                    setTimeout(() => {
                        successMessage.style.display = 'none';
                    }, 5000);
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('There was a problem sending your message. Please try again later.');
                })
                .finally(() => {
                    // Reset button state
                    submitBtn.disabled = false;
                    submitSpinner.style.display = 'none';
                });
            }
        });
    </script>
</body>
</html>









