const jobManager = {
    getAllJobs: async () => {
        try {
            const response = await fetch('/api/jobs');
            const text = await response.text();
            
            if (!response.ok) {
                console.error('API Error Response:', text);
                throw new Error(`HTTP ${response.status}: ${text.substring(0, 100)}`);
            }

            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Failed to parse JSON:', text);
                throw new Error('Invalid API response format');
            }
        } catch (error) {
            console.error('Network error:', error);
            throw new Error('Failed to connect to server');
        }
    },

    deleteJob: async (jobId) => {
        try {
            const response = await fetch(`/api/jobs?id=${jobId}`, {
                method: 'DELETE'
            });
            const text = await response.text();
            
            if (!response.ok) {
                console.error('Delete Error:', text);
                throw new Error(`Delete failed: ${text.substring(0, 100)}`);
            }

            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Invalid delete response:', text);
                throw new Error('Unexpected server response');
            }
        } catch (error) {
            console.error('Delete error:', error);
            throw new Error('Failed to delete job');
        }
    }
}; 

async function loadAdminJobs() {
    try {
        const response = await jobManager.getAllJobs();
        if (!response.success) throw new Error(response.error);
        
        const adminJobsList = document.getElementById('adminJobsList');
        adminJobsList.innerHTML = '';
        
        if (!response.data || response.data.length === 0) {
            adminJobsList.innerHTML = '<div class="no-jobs">لا توجد وظائف منشورة بعد</div>';
            return;
        }
        
        response.data.forEach((job) => {
            const jobItem = document.createElement('div');
            jobItem.className = 'admin-job-item';
            jobItem.innerHTML = `
                <div class="job-info">
                    <h4>${job.title}</h4>
                    <p>${job.company}</p>
                    <p>${job.location}</p>
                    <small>${job.description.substring(0, 100)}...</small>
                </div>
                <button onclick="deleteJobById('${job.id}')" class="delete-btn">حذف</button>
            `;
            adminJobsList.appendChild(jobItem);
        });
    } catch (error) {
        console.error('Error loading admin jobs:', error);
        const adminJobsList = document.getElementById('adminJobsList');
        adminJobsList.innerHTML = `
            <div class="error-message">
                خطأ في تحميل الوظائف: ${error.message}
                <button onclick="location.reload()">إعادة المحاولة</button>
            </div>
        `;
    }
} 