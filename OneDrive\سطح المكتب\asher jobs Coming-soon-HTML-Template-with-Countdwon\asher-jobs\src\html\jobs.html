<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Asher Jobs connects candidates with suitable job opportunities and provides training for recruiters to enhance hiring processes.">
    <meta name="keywords" content="jobs, recruitment, career opportunities, job training, Asher Jobs, employee matching, recruiter training, professional growth">
    <meta name="author" content="Asher Jobs - وظائف العاشر">
    <meta name="geo.region" content="EG">
    <meta name="geo.placename" content="10th of Ramadan City">
    <meta name="geo.position" content="30.2967;31.7433">
    <meta name="ICBM" content="30.2967, 31.7433">
    

     <!-- Favicon -->
    <link rel="icon" href="../images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../images/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="../images/favicon.ico">
    <meta name="theme-color" content="#003C43">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Main custom css -->
    <link href="../css/custom.css" rel="stylesheet" media="screen">
    <link href="../css/custom-ar.css" rel="stylesheet" media="screen">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- Google Ads -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2136749359648243" crossorigin="anonymous"></script>
    <script async custom-element="amp-auto-ads" src="https://cdn.ampproject.org/v0/amp-auto-ads-0.1.js"></script>
    
    <!-- Page Title -->
    <title>Asher Jobs وظائف العاشر</title>
    
    <!-- Favicon -->
    <link rel="icon" href="src/images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="src/images/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="src/images/favicon.ico">
    <meta name="theme-color" content="#003C43">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">

    
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- Main CSS - Order matters: base styles first -->
    <link href="src/css/style.css" rel="stylesheet" media="screen">
    <link href="src/css/custom.css" rel="stylesheet" media="screen">
    
    <!-- Google Ads -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2136749359648243" crossorigin="anonymous"></script>
    <script async custom-element="amp-auto-ads" src="https://cdn.ampproject.org/v0/amp-auto-ads-0.1.js"></script>
    <script src="../js/jobManager.js"></script>
</head>
<body>
  <amp-auto-ads type="adsense"
  data-ad-client="ca-pub-2136749359648243">
</amp-auto-ads>

	
<amp-auto-ads type="adsense"
data-ad-client="ca-pub-2136749359648243">
</amp-auto-ads>

<header>
<h1>وظائف شاغرة في العاشر من رمضان</h1>
<form class="search-bar" id="searchForm">
    <div class="search-input-container">
        <input type="text" id="searchInput" placeholder="ابحث عن وظيفة...">
    </div>
    <div class="search-button-container">
        <button type="submit">بحث</button>
    </div>
</form>
<div class="nav-links">
    <a href="src\html\index.html">الرئيسية</a>

    
</div>
</header>

<!-- Loading Spinner -->
<div id="loadingSpinner" class="loading-spinner" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
    <p>Loading jobs...</p>
</div>

<style>
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin: 20px auto;
    max-width: 200px;
}

.loading-spinner p {
    margin-top: 10px;
    font-weight: bold;
}
</style>

<!-- Market Analysis Section -->
<div class="market-analysis">
    <div class="analysis-container">
        <h2>دليل سوق العمل في العاشر من رمضان</h2>
        
        <div class="analysis-section">
            <h3>السياق الاقتصادي والصناعي</h3>
            <p>تتميز مدينة العاشر من رمضان بتركيزها الصناعي القوي، حيث تضم:</p>
            <ul>
                <li>مجمع صناعي كبير يشمل مصانع متعددة</li>
                <li>مركز لوجستي رئيسي</li>
                <li>قطاعات خدمات وتكنولوجيا متنامية</li>
                <li>فرص عمل متنوعة في مختلف المجالات</li>
            </ul>
        </div>
    </div>
</div>

<style>
.market-analysis {
    background: #f8f9fa;
    padding: 30px 0;
    margin: 20px 0;
    border-radius: 8px;
    direction: rtl;
}

.analysis-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
    direction: rtl;
}

.analysis-container h2 {
    color: #003C43;
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
}

.analysis-section {
    background: white;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    direction: rtl;
}

.analysis-section h3 {
    color: #77B0AA;
    margin-bottom: 15px;
    font-size: 18px;
    text-align: right;
}

.analysis-section ul {
    list-style: none;
    padding: 0;
    direction: rtl;
}

.analysis-section ul li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    color: #666;
    text-align: right;
}

.analysis-section ul li:last-child {
    border-bottom: none;
}

@media (max-width: 768px) {
    .analysis-container {
        padding: 0 15px;
    }
    
    .analysis-section {
        padding: 15px;
    }
}

.nav-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 20px 0;
    direction: rtl;
}

.nav-links a {
    padding: 10px 20px;
    text-decoration: none;
    color: #333;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.nav-links a:hover {
    background-color: #77B0AA;
    color: white;
}

.nav-links a.active {
    background-color: #77B0AA;
    color: white;
}
</style>

<!-- Admin Panel -->
<div id="adminPanel" style="display: none;">
    <div class="admin-panel">
        <h3>لوحة التحكم</h3>
        <div class="admin-controls">
            <button onclick="toggleAdminPanel()" class="admin-close">✕</button>
            <button onclick="window.location.href='posting.html'" class="admin-btn">إضافة وظيفة جديدة</button>
            <button onclick="logout()" class="admin-btn">تسجيل الخروج</button>
        </div>
        <div class="admin-jobs-list">
            <h4>الوظائف المنشورة</h4>
            <div id="adminJobsList"></div>
        </div>
    </div>
</div>

<div class="job-listings">
<!-- Job Ads will be dynamically loaded here -->

</div>

<!-- Pagination Controls -->
<div class="pagination-container">
    <div class="pagination">
        <button id="prevPage" class="pagination-btn">Previous</button>
        <div id="pageNumbers" class="page-numbers"></div>
        <button id="nextPage" class="pagination-btn">Next</button>
    </div>
</div>

<!-- Schema.org Structured Data for SEO -->
<script type="application/ld+json">
{
"@context": "https://schema.org",
"@type": "JobPosting",
"title": "فني صيانة",
"hiringOrganization": "شركة المصنع الحديث",
"jobLocation": "العاشر من رمضان"
}
</script>

<style>
.admin-panel {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 20px auto;
    max-width: 800px;
    direction: rtl;
}

.admin-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.admin-btn {
    padding: 8px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.admin-close {
    padding: 8px 16px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.admin-job-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.delete-btn {
    padding: 5px 10px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.job-info {
    flex: 1;
}

.job-info h4 {
    margin: 0 0 5px 0;
}

.job-info p {
    margin: 0 0 5px 0;
    color: #666;
}

.job-info small {
    color: #999;
}

.pagination-container {
    margin: 30px auto;
    text-align: center;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.pagination-btn {
    background: #77B0AA;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.pagination-btn:hover {
    background: #003C43;
}

.pagination-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 5px;
}

.page-number {
    padding: 8px 12px;
    border: 1px solid #77B0AA;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.page-number.active {
    background: #77B0AA;
    color: white;
}

.page-number:hover:not(.active) {
    background: #f0f0f0;
}

@media (max-width: 768px) {
    .pagination {
        flex-wrap: wrap;
    }
    
    .page-numbers {
        order: 3;
        width: 100%;
        justify-content: center;
        margin: 10px 0;
    }
}

.search-bar {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 600px;
    margin: 20px auto;
    padding: 0 20px;
}

.search-input-container {
    width: 100%;
}

.search-bar input {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #77B0AA;
    border-radius: 4px;
    font-size: 16px;
}

.search-button-container {
    width: 100%;
    text-align: center;
}

.search-bar button {
    padding: 10px 40px;
    background: #77B0AA;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 16px;
}

.search-bar button:hover {
    background: #003C43;
}

.no-results {
    text-align: center;
    padding: 20px;
    color: #666;
    font-size: 18px;
}

.job-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    color: #333;
}

.job-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.job-card-content {
    direction: rtl;
}

.job-title {
    color: #003C43;
    margin-bottom: 15px;
    font-size: 1.5em;
}

.job-company, .job-location, .job-description {
    margin: 10px 0;
    color: #333;
}

.job-company strong, .job-location strong, .job-description strong {
    color: #003C43;
    margin-left: 5px;
}

.no-jobs {
    text-align: center;
    padding: 20px;
    color: #333;
    font-size: 18px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.error-message {
    text-align: center;
    padding: 20px;
    color: #dc3545;
    font-size: 18px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', async function() {
    const jobsContainer = document.querySelector('.job-listings');
    const searchInput = document.getElementById('searchInput');
    const searchButton = document.querySelector('.search-button-container button');
    const loadingSpinner = document.getElementById('loadingSpinner');
    
    if (!jobsContainer) {
        console.error('Job listings container not found');
        return;
    }
    
    // Function to show/hide loading spinner
    function toggleLoading(show) {
        if (loadingSpinner) {
            loadingSpinner.style.display = show ? 'block' : 'none';
        }
    }

    // Function to display jobs
    function displayJobs(jobs) {
        jobsContainer.innerHTML = '';
        if (!jobs || jobs.length === 0) {
            jobsContainer.innerHTML = '<div class="no-jobs">لا توجد وظائف متاحة حال</div>';
            return;
        }

        console.log('Displaying jobs:', jobs.length);
        jobs.forEach(job => {
            const jobCard = document.createElement('div');
            jobCard.className = 'job-card';
            jobCard.innerHTML = `
                <div class="job-card-content">
                    <h3 class="job-title">${job.title || 'No Title'}</h3>
                    <p class="job-company"><strong>Company:</strong> ${job.company || 'Not specified'}</p>
                    <p class="job-location"><strong>Location:</strong> ${job.location || 'Not specified'}</p>
                    <p class="job-description"><strong>Description:</strong> ${job.description || 'No description available'}</p>
                    ${job.requirements ? `<p class="job-requirements"><strong>Requirements:</strong> ${job.requirements}</p>` : ''}
                </div>
            `;
            jobsContainer.appendChild(jobCard);
        });
    }

    // Function to load all jobs
    async function loadJobs() {
        try {
            toggleLoading(true);
            console.log('Loading jobs from domain:', window.location.hostname);
            
            // First try to get jobs from localStorage
            let jobs = [];
            try {
                const jobsJson = localStorage.getItem('asherJobs');
                if (jobsJson) {
                    jobs = JSON.parse(jobsJson);
                    console.log('Found jobs in localStorage:', jobs.length);
                }
            } catch (e) {
                console.error('Error reading from localStorage:', e);
            }
            
            // If no jobs in localStorage, try to get them from the API
            if (!jobs || jobs.length === 0) {
                console.log('No jobs in localStorage, trying API');
                jobs = await jobManager.getAllJobs();
                console.log('Jobs loaded from API:', jobs.length);
            }
            
            // If still no jobs, initialize sample jobs
            if (!jobs || jobs.length === 0) {
                console.log('No jobs found, initializing sample jobs');
                jobs = jobManager.initializeSampleJobs();
                console.log('Sample jobs initialized:', jobs.length);
            }
            
            displayJobs(jobs);
        } catch (error) {
            console.error('Error loading jobs:', error);
            jobsContainer.innerHTML = '<div class="error-message">Error loading jobs. Please try again later.</div>';
        } finally {
            toggleLoading(false);
        }
    }

    // Function to search jobs
    async function searchJobs(query) {
        try {
            toggleLoading(true);
            console.log('Searching jobs for query:', query);
            const jobs = await jobManager.searchJobs(query);
            console.log('Search results:', jobs.length);
            displayJobs(jobs);
        } catch (error) {
            console.error('Error searching jobs:', error);
            jobsContainer.innerHTML = '<div class="error-message">Error searching jobs. Please try again later.</div>';
        } finally {
            toggleLoading(false);
        }
    }

    // Event listener for search button
    if (searchButton) {
        searchButton.addEventListener('click', async () => {
            const query = searchInput ? searchInput.value.trim() : '';
            if (query) {
                await searchJobs(query);
            } else {
                await loadJobs();
            }
        });
    }

    // Event listener for search input (Enter key)
    if (searchInput) {
        searchInput.addEventListener('keypress', async (e) => {
            if (e.key === 'Enter') {
                const query = searchInput.value.trim();
                if (query) {
                    await searchJobs(query);
                } else {
                    await loadJobs();
                }
            }
        });
    }

    // Initial load of jobs
    await loadJobs();
});

// Function to delete a job
async function deleteJobById(index) {
  if (confirm('هل أنت متأكد من حذف هذه الوظيفة؟')) {
    try {
      toggleLoading(true);
      const jobs = await jobManager.getAllJobs();
      const jobToDelete = jobs[index];
      if (!jobToDelete) {
        alert('Job not found');
        return;
      }
      const result = await jobManager.deleteJob(jobToDelete.id);
      if (result.success) {
        // Refresh both displays
        await loadJobs();
        await loadAdminJobs();
      } else {
        alert(result.error || 'Failed to delete job. Please try again.');
      }
    } catch (error) {
      console.error('Error deleting job:', error);
      alert('An error occurred while deleting the job. Please try again.');
    } finally {
      toggleLoading(false);
    }
  }
}

// Function to load admin jobs
async function loadAdminJobs() {
    try {
        toggleLoading(true);
        const jobs = await jobManager.getAllJobs();
        const adminJobsList = document.getElementById('adminJobsList');
        if (!adminJobsList) {
            console.error('Admin jobs list container not found');
            return;
        }
        
        adminJobsList.innerHTML = '';
        
        jobs.forEach((job, index) => {
            const jobItem = document.createElement('div');
            jobItem.className = 'admin-job-item';
            jobItem.innerHTML = `
                <div class="job-info">
                    <h4>${job.title}</h4>
                    <p>${job.company}</p>
                    <p>${job.location}</p>
                    <small>${job.description}</small>
                </div>
                <button onclick="deleteJobById(${index})" class="delete-btn">Delete</button>
            `;
            adminJobsList.appendChild(jobItem);
        });
    } catch (error) {
        console.error('Error loading admin jobs:', error);
        const adminJobsList = document.getElementById('adminJobsList');
        adminJobsList.innerHTML = `<div class="error-message">Error loading jobs: ${error.message}</div>`;
    } finally {
        toggleLoading(false);
    }
}

// Function to toggle admin panel
function toggleAdminPanel() {
    const panel = document.getElementById('adminPanel');
    panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
}

// Function to logout
function logout() {
    sessionStorage.removeItem('authenticated');
    window.location.reload();
}

const POSTS_PER_PAGE = 15;
let currentPage = 1;
let totalPages = 1;

function updatePagination(currentPage) {
    const pageNumbers = document.getElementById('pageNumbers');
    pageNumbers.innerHTML = '';
    
    // Show first page
    if (currentPage > 2) {
        addPageNumber(1);
        if (currentPage > 3) {
            pageNumbers.innerHTML += '<span>...</span>';
        }
    }
    
    // Show pages around current page
    for (let i = Math.max(1, currentPage - 1); i <= Math.min(totalPages, currentPage + 1); i++) {
        addPageNumber(i);
    }
    
    // Show last page
    if (currentPage < totalPages - 1) {
        if (currentPage < totalPages - 2) {
            pageNumbers.innerHTML += '<span>...</span>';
        }
        addPageNumber(totalPages);
    }
    
    // Update button states
    document.getElementById('prevPage').disabled = currentPage === 1;
    document.getElementById('nextPage').disabled = currentPage === totalPages;
}

function addPageNumber(pageNum) {
    const pageNumbers = document.getElementById('pageNumbers');
    const pageButton = document.createElement('button');
    pageButton.className = `page-number ${pageNum === currentPage ? 'active' : ''}`;
    pageButton.textContent = pageNum;
    pageButton.onclick = () => {
        currentPage = pageNum;
        loadJobs(currentPage);
    };
    pageNumbers.appendChild(pageButton);
}

// Event Listeners
document.getElementById('prevPage').addEventListener('click', () => {
    if (currentPage > 1) {
        currentPage--;
        loadJobs(currentPage);
    }
});

document.getElementById('nextPage').addEventListener('click', () => {
    if (currentPage < totalPages) {
        currentPage++;
        loadJobs(currentPage);
    }
});

// Add search functionality
document.getElementById('searchForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const searchQuery = document.getElementById('searchInput').value;
    currentPage = 1; // Reset to first page when searching
    loadJobs(1, searchQuery);
});
</script>
     <!-- Footer: Modern, Accessible, and Responsive (English) -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-brand">
                <a href="../html/indexar.html" class="footer-logo" aria-label="Asher Jobs Home">
                    <img src="../images/logo.png" alt="Asher Jobs Logo" height="60" loading="lazy">
                    <h3>Asher Jobs</h3>
                </a>
               
                <p class="footer-tagline">Connecting Talent with Opportunity</p>
            </div>
            <nav class="footer-nav" aria-label="Footer Navigation">
                <ul class="footer-links">
                    <li><a href="../html/jobs.html">Browse Jobs</a></li>
                    <li><a href="../html/adminLogin.html">Admin Login</a></li>
                    <li><a href="../html/contact.html">Contact Us</a></li>
                    <li><a href="../html/privacy.html">Privacy Policy</a></li>
                </ul>
            </nav>
            <div class="footer-social">
                
                <div class="social-icons">
                    <a href="https://www.facebook.com/profile.php?id=61566379346391" aria-label="Facebook" class="fa fa-facebook" tabindex="0"></a>
                    <a href="https://www.linkedin.com/company/3ashr-jobs/?viewAsMember=true" aria-label="LinkedIn" class="fa fa-linkedin" tabindex="0"></a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 Asher Jobs. All rights reserved.</p>
        </div>
  <style>
    /* Basic Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.site-footer {
  background-color: #003C43;
  color: #f1f1f1;
  padding: 40px 20px 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2rem;
}

.footer-brand {
  flex: 1 1 250px;
}

.footer-brand h3 {
  font-size: 1.5rem;
  margin-top: 10px;
  color: #ffffff;
}

.footer-tagline {
  font-size: 0.95rem;
  color: #cfcfcf;
  margin-top: 5px;
}

.footer-logo img {
  max-width: 100%;
  height: 100px;
  border-radius: 5px;
}

.footer-nav {
  flex: 1 1 200px;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-top: 20px;
}

.footer-links a {
  color: #cfcfcf;
  display: inline-block;
  transition: transform 0.3s ease;
}

.footer-links a:hover,
.footer-links a:focus {
  color: #77B0AA;
  text-decoration: none;
    transform: scale(1.1);
}

.footer-social {
  flex: 1 1 200px;
  margin-top: 70px;
}

.footer-social p {
  font-weight: bold;
  margin-bottom: 10px;
}

.social-icons a {
  display: inline-block;
  font-size: 1.25rem;
  margin-right: 15px;
  color: #cfcfcf;
  transition: color 0.3s, transform 0.3s;
}

.social-icons a:hover,
.social-icons a:focus {
  color: #ffffff;
  transform: scale(1.1);
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  font-size: 0.875rem;
  color: #999;
  border-top: 1px solid #444;
  margin-top: 30px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .footer-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .footer-social, .footer-brand, .footer-nav {
    flex: 1 1 100%;
  }

  .social-icons a {
    margin: 0 10px;
  }
}

  </style>
    </footer>
<script src="../js/jobAnalysis.js"></script>
</html>
