# Enable rewrite engine
RewriteEngine On

# Set base directory
RewriteBase /

# Set Content Security Policy
Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://static.cloudflareinsights.com https://pagead2.googlesyndication.com https://cdn.ampproject.org; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://static.cloudflareinsights.com;"

# Handle CORS
Header set Access-Control-Allow-Origin "*"
Header set Access-Control-Allow-Methods "GET, POST, DELETE, OPTIONS"
Header set Access-Control-Allow-Headers "Content-Type, Authorization, Accept"
Header set Access-Control-Max-Age "86400"

# Handle OPTIONS requests
RewriteCond % { REQUEST_METHOD } OPTIONS
RewriteRule ^(.*)$ $1 [R=200, L]

# Prevent directory listing
Options -Indexes

# Protect sensitive files
<FilesMatch "^(\.htaccess|\.htpasswd|\.git|\.env|composer\.json|composer\.lock|package\.json|package-lock\.json)$">
Order allow, deny
Deny from all
</FilesMatch>

# Enable compression
<IfModule mod_deflate.c>
AddOutputFilterByType DEFLATE text/plain
AddOutputFilterByType DEFLATE text/html
AddOutputFilterByType DEFLATE text/xml
AddOutputFilterByType DEFLATE text/css
AddOutputFilterByType DEFLATE application/xml
AddOutputFilterByType DEFLATE application/xhtml+xml
AddOutputFilterByType DEFLATE application/rss+xml
AddOutputFilterByType DEFLATE application/javascript
AddOutputFilterByType DEFLATE application/x-javascript
AddOutputFilterByType DEFLATE application/json
</IfModule>

# Set caching headers
<IfModule mod_expires.c>
ExpiresActive On
ExpiresByType image/jpg "access plus 1 year"
ExpiresByType image/jpeg "access plus 1 year"
ExpiresByType image/gif "access plus 1 year"
ExpiresByType image/png "access plus 1 year"
ExpiresByType image/webp "access plus 1 year"
ExpiresByType text/css "access plus 1 month"
ExpiresByType application/javascript "access plus 1 month"
ExpiresByType text/javascript "access plus 1 month"
ExpiresByType application/x-javascript "access plus 1 month"
ExpiresByType text/html "access plus 1 day"
ExpiresByType application/xhtml+xml "access plus 1 day"
</IfModule>

# Redirect to HTTPS
RewriteCond % { HTTPS } off
RewriteRule ^(.*)$ https://% { HTTP_HOST }% { REQUEST_URI } [L, R=301]

# Handle 404 errors
ErrorDocument 404 /404.html

# Handle 500 errors
ErrorDocument 500 /500.html

# Prevent access to hidden files and directories
RewriteCond % { SCRIPT_FILENAME } -d [OR]
RewriteCond % { SCRIPT_FILENAME } -f
RewriteRule "(^|/)\." - [F]

# Prevent access to backup and source files
<FilesMatch "(\.(bak|config|sql|fla|psd|ini|log|sh|inc|swp|dist)|~)$">
Order allow, deny
Deny from all
Satisfy All
</FilesMatch>

# Set default character set
AddDefaultCharset UTF-8

# Enable Keep-Alive
<IfModule mod_headers.c>
Header set Connection keep-alive
</IfModule>

# Set security headers
<IfModule mod_headers.c>
Header set X-Content-Type-Options "nosniff"
Header set X-XSS-Protection "1; mode=block"
Header set X-Frame-Options "SAMEORIGIN"
Header set Referrer-Policy "strict-origin-when-cross-origin"
Header set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Allow all methods for API directory
<Directory "/api">
Order allow, deny
Allow from all
Satisfy all
Header set Access-Control-Allow-Origin "*"
Header set Access-Control-Allow-Methods "GET, POST, DELETE, OPTIONS"
Header set Access-Control-Allow-Headers "Content-Type, Authorization, Accept"
Header set Access-Control-Max-Age "86400"
</Directory>

# Redirect PHP requests to HTML equivalents
RewriteRule ^api/test\.php$ /api/test.html [R=301, L]
