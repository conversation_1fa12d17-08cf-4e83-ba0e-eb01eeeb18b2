<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Asher Jobs connects candidates with suitable job opportunities and provides training for recruiters to enhance hiring processes.">
    <meta name="keywords" content="jobs, recruitment, career opportunities, job training, Asher Jobs, employee matching, recruiter training, professional growth">
    <meta name="author" content="Asher Jobs - وظائف العاشر">
    <meta name="geo.region" content="EG">
    <meta name="geo.placename" content="10th of Ramadan City">
    <meta name="geo.position" content="30.2967;31.7433">
    <meta name="ICBM" content="30.2967, 31.7433">
    

     <!-- Favicon -->
    <link rel="icon" href="../images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../images/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="../images/favicon.ico">
    <meta name="theme-color" content="#003C43">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Main custom css -->
    <link href="../css/custom.css" rel="stylesheet" media="screen">
    <link href="../css/custom-ar.css" rel="stylesheet" media="screen">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- Google Ads -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2136749359648243" crossorigin="anonymous"></script>
    <script async custom-element="amp-auto-ads" src="https://cdn.ampproject.org/v0/amp-auto-ads-0.1.js"></script>
    
    <!-- Page Title -->
    <title>Asher Jobs وظائف العاشر</title>
    
    <!-- Favicon -->
    <link rel="icon" href="src/images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="src/images/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="src/images/favicon.ico">
    <meta name="theme-color" content="#003C43">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">

    
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- Main CSS - Order matters: base styles first -->
    <link href="src/css/style.css" rel="stylesheet" media="screen">
    <link href="src/css/custom.css" rel="stylesheet" media="screen">
    
    <!-- Google Ads -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2136749359648243" crossorigin="anonymous"></script>
    <script async custom-element="amp-auto-ads" src="https://cdn.ampproject.org/v0/amp-auto-ads-0.1.js"></script>
    <style>
.wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: #003C43;
    padding: 20px;
}

form {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    direction: rtl;
}

h1 {
    color: #003C43;
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
    font-weight: 600;
}

.input-box {
    position: relative;
    margin-bottom: 20px;
}

.input-box input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.input-box input:focus {
    border-color: #77B0AA;
    outline: none;
    background: white;
}

.input-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #77B0AA;
}

.remember-forget {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 14px;
}

.remember-forget label {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    cursor: pointer;
}

.remember-forget input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #77B0AA;
}

.btn {
    width: 100%;
    padding: 12px;
    background: #77B0AA;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background: #003C43;
}

@media (max-width: 480px) {
    form {
        padding: 30px 20px;
    }
    
    h1 {
        font-size: 20px;
    }
    
    .input-box input {
        padding: 10px 12px;
    }
}
</style>
</head>
<body>
  <amp-auto-ads type="adsense"
  data-ad-client="ca-pub-2136749359648243">
</amp-auto-ads>

	

<div class="wrapper">
  <form id="adminLoginForm">
      <h1>Login</h1>
      <div class="input-box">
        <input type="text" placeholder="Username" required>
          <i class='bx bxs-user'></i>
      </div>
      <div class="input-box">
          <input type="password" placeholder="Password" required>
          <i class='bx bxs-lock-alt'></i>
      </div>
      <div class="remember-forget">
          <label><input type="checkbox">Remember me</label>
      </div>
      <button type="submit" class="btn">Login</button>
      
  </form>
</div>
<script>
// Prevent right-click
document.addEventListener('contextmenu', function(e) {
    e.preventDefault();
});

// Prevent F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
document.addEventListener('keydown', function(e) {
    if (
        e.key === 'F12' || 
        (e.ctrlKey && e.shiftKey && e.key === 'I') || 
        (e.ctrlKey && e.shiftKey && e.key === 'J') || 
        (e.ctrlKey && e.key === 'u')
    ) {
        e.preventDefault();
    }
});

// Handle form submission
document.getElementById('adminLoginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const username = document.querySelector('input[type="text"]').value;
    const password = document.querySelector('input[type="password"]').value;
    
    // Check against the provided credentials
    if (username === '<EMAIL>' && password === 'Oh01068214863') {
        // Set authentication state
        sessionStorage.setItem('authenticated', 'true');
        
        // Redirect to posting page
        window.location.href = 'posting.html';
    } else {
        alert('Invalid credentials. Please try again.');
        console.log('Login attempt failed. Username:', username);
    }
});
</script>

</body>

      <!-- Footer: Modern, Accessible, and Responsive (English) -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-brand">
                <a href="../html/indexar.html" class="footer-logo" aria-label="Asher Jobs Home">
                    <img src="../images/logo.png" alt="Asher Jobs Logo" height="60" loading="lazy">
                    <h3>Asher Jobs</h3>
                </a>
               
                <p class="footer-tagline">Connecting Talent with Opportunity</p>
            </div>
            <nav class="footer-nav" aria-label="Footer Navigation">
                <ul class="footer-links">
                    <li><a href="../html/jobs.html">Browse Jobs</a></li>
                    <li><a href="../html/adminLogin.html">Admin Login</a></li>
                    <li><a href="../html/contact.html">Contact Us</a></li>
                    <li><a href="../html/privacy.html">Privacy Policy</a></li>
                </ul>
            </nav>
            <div class="footer-social">
                
                <div class="social-icons">
                    <a href="https://www.facebook.com/profile.php?id=61566379346391" aria-label="Facebook" class="fa fa-facebook" tabindex="0"></a>
                    <a href="https://www.linkedin.com/company/3ashr-jobs/?viewAsMember=true" aria-label="LinkedIn" class="fa fa-linkedin" tabindex="0"></a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 Asher Jobs. All rights reserved.</p>
        </div>
  <style>
    /* Basic Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.site-footer {
  background-color: #003C43;
  color: #f1f1f1;
  padding: 40px 20px 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2rem;
}

.footer-brand {
  flex: 1 1 250px;
}

.footer-brand h3 {
  font-size: 1.5rem;
  margin-top: 10px;
  color: #ffffff;
}

.footer-tagline {
  font-size: 0.95rem;
  color: #cfcfcf;
  margin-top: 5px;
}

.footer-logo img {
  max-width: 100%;
  height: 100px;
  border-radius: 5px;
}

.footer-nav {
  flex: 1 1 200px;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-top: 20px;
}

.footer-links a {
  color: #cfcfcf;
  display: inline-block;
  transition: transform 0.3s ease;
}

.footer-links a:hover,
.footer-links a:focus {
  color: #77B0AA;
  text-decoration: none;
    transform: scale(1.1);
}

.footer-social {
  flex: 1 1 200px;
  margin-top: 70px;
}

.footer-social p {
  font-weight: bold;
  margin-bottom: 10px;
}

.social-icons a {
  display: inline-block;
  font-size: 1.25rem;
  margin-right: 15px;
  color: #cfcfcf;
  transition: color 0.3s, transform 0.3s;
}

.social-icons a:hover,
.social-icons a:focus {
  color: #ffffff;
  transform: scale(1.1);
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  font-size: 0.875rem;
  color: #999;
  border-top: 1px solid #444;
  margin-top: 30px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .footer-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .footer-social, .footer-brand, .footer-nav {
    flex: 1 1 100%;
  }

  .social-icons a {
    margin: 0 10px;
  }
}

  </style>
</html>