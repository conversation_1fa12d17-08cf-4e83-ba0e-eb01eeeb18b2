<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Asher Jobs connects candidates with suitable job opportunities and provides training for recruiters to enhance hiring processes.">
    <meta name="keywords" content="jobs, recruitment, career opportunities, job training, Asher Jobs, employee matching, recruiter training, professional growth">
    <meta name="author" content="Asher Jobs - وظائف العاشر (عاشر جوبز
    )">
    <meta name="geo.region" content="EG">
    <meta name="geo.placename" content="10th of Ramadan City">
    <meta name="geo.position" content="30.2967;31.7433">
    <meta name="ICBM" content="30.2967, 31.7433">
    

     <!-- Favicon -->
    <link rel="icon" href="../images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../images/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="../images/favicon.ico">
    <meta name="theme-color" content="#003C43">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Main custom css -->
    <link href="../css/custom.css" rel="stylesheet" media="screen">
    <link href="../css/custom-ar.css" rel="stylesheet" media="screen">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- Google Ads -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2136749359648243" crossorigin="anonymous"></script>
    <script async custom-element="amp-auto-ads" src="https://cdn.ampproject.org/v0/amp-auto-ads-0.1.js"></script>
    
    <!-- Page Title -->
    <title>Asher Jobs وظائف العاشر</title>
    
    <!-- Favicon -->
    <link rel="icon" href=".../images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../images/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="../images/favicon.ico">
    <meta name="theme-color" content="#003C43">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">

    
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- Main CSS - Order matters: base styles first -->
    <link href="../css/style.css" rel="stylesheet" media="screen">
    <link href="../css/custom.css" rel="stylesheet" media="screen">
    
    <!-- Google Ads -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2136749359648243" crossorigin="anonymous"></script>
    <script async custom-element="amp-auto-ads" src="https://cdn.ampproject.org/v0/amp-auto-ads-0.1.js"></script>
    
    <!-- Countdown Script -->
    <script src="../js/countdown.js" defer></script>
</head>
<body>
    <amp-auto-ads type="adsense" data-ad-client="ca-pub-2136749359648243"></amp-auto-ads>

    <!-- Header with Logo -->
    <header class="site-header">
        <div class="container">
            <div class="header-content">
                <div class="site-logo">
                    <a href="indexar.html">
                        <img src="../images/logo.png" alt="Asher Jobs Logo" height="100">
                    </a>
                </div>
                
                <!-- Language Switcher -->
                <div class="language-switcher">
                    <a href="../html/index.html" class="lang-switch-btn">
                        <span class="lang-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path fill="currentColor" d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-2.29-2.333A17.9 17.9 0 0 1 8.027 13H4.062a8.008 8.008 0 0 0 5.648 6.667zM10.03 13c.151 2.439.848 4.73 1.97 6.752A15.905 15.905 0 0 0 13.97 13h-3.94zm9.908 0h-3.965a17.9 17.9 0 0 1-1.683 6.667A8.008 8.008 0 0 0 19.938 13zM4.062 11h3.965A17.9 17.9 0 0 1 9.71 4.333 8.008 8.008 0 0 0 4.062 11zm5.969 0h3.938A15.905 15.905 0 0 0 12 4.248 15.905 15.905 0 0 0 10.03 11zm4.259-6.667A17.9 17.9 0 0 1 15.938 11h3.965a8.008 8.008 0 0 0-5.648-6.667z"/>
                            </svg>
                        </span>
                        <span class="lang-text">العربية</span>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero section with clear value proposition -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1>Connecting Talent with Opportunity in 10th of Ramadan</h1>
                <p>Asher Jobs is bridging the gap between talent and opportunity, helping companies find the perfect candidates while empowering job seekers to grow their careers.</p>
                
                <div class="hero-cta">
                    <a href="../html/jobs.html" class="btn btn-primary">Browse Jobs</a>
                    
                </div>
                
                <!-- Countdown timer -->
              
            </div>
        </div>
    </section>

    <!-- Floating Sitemap -->
    <div class="floating-sitemap">
        <button class="sitemap-toggle">☰</button>
        <div class="dropdown-menu">
            <h3>Site Map</h3>
            <ul>
                <p>Articles Shortcut</p>
                <li><a href="#job-tips">⦁ How to get accepted in your dream Job?</a></li>
                <li><a href="#resume-guide">⦁ Components of a Professional Resume</a></li>
                <li><a href="#video-guide">⦁ What is ATS?</a></li>
                <li><a href="#a-hunt">⦁ A Hunt Service</a></li>
            </ul>
        </div>
    </div>
    
    <!-- WhatsApp Channels Section -->
    <section class="wa"> 
        <div class="wacontainer">
            <h2 class="title2">
                <i class="fa fa-whatsapp" aria-hidden="true"></i> 
                Join Our WhatsApp channel for most recent Jobs
            </h2>
            <div class="image-container">
                <a href="https://www.whatsapp.com/channel/0029Vb8hBqrFCCocCAxIi50L">
                    <div class="qr"></div>
                </a>
            </div>        
        </div>

        <div>
            <h2>Are you student at university? Join our WhatsApp channel for internships jobs.</h2>
            <div class="wacontainer">
                <h2 class="title2">
                    <i class="fa fa-whatsapp" aria-hidden="true"></i> 
                    A InternHub & Jobs (Engineers) channel
                </h2>
                <div class="image-container">
                    <a href="https://whatsapp.com/channel/0029VbAQ8IO1yT21jcZBNN0g">
                        <div class="qr2"></div>
                    </a>
                </div>        
            </div>
            <div class="wacontainer">
                <h2 class="title2">
                    <i class="fa fa-whatsapp" aria-hidden="true"></i> 
                    A InternHub & Jobs (Science, Pharmacy & Agriculture) channel
                </h2>
                <div class="image-container">
                    <a href="https://whatsapp.com/channel/0029Vb62J9X2ZjCr5CSyOC1J">
                        <div class="qr3"></div>
                    </a>
                </div>        
            </div>
            <div class="wacontainer">
                <h2 class="title2">
                    <i class="fa fa-whatsapp" aria-hidden="true"></i> 
                    A InternHub & Jobs (Developers)
                </h2>
                <div class="image-container">
                    <a href="https://whatsapp.com/channel/0029Vb5gEOm5q08TOzQbU215">
                        <div class="qr4"></div>
                    </a>
                </div>        
            </div>

           
        </div>
    </section>

    <!-- Articles Section -->
    <div class="comming-soon fade-in">
        <!-- Job Tips Article -->
        <div class="contact-section fade-in">
            <div class="contact-form"> 
                <div class="contact-box">
                    <h2 class="title" id="job-tips">How to get accepted in your dream Job?</h2>
                    <div class="article-content">
                        <p>
                            A lot of people ask why they didn't get a callback after submitting their resume. Here are some tips that might help you get a call for an interview:
                        </p>
                        <p>
                            <strong>1- Resume for each company:</strong> There is no way you are still using only one resume to apply for your jobs. You need to modify your resume each time you apply to a job. You'll understand why after reading the following tips.
                        </p>
                        
                        <div class="hidden-content">
                            <p>
                                <strong>2- Simplify your resume:</strong> Some people think it's a good idea to make a colorful resume to attract the person reading it, but this might prevent it from passing through ATS systems.
                            </p>
                            <div class="pic">
                                <img src="../images/Apg.jpg" alt="Resume example">
                            </div>
                            <p>
                                <strong>3- Pass ATS:</strong> These monster systems often kill your dreams.
                            </p>
                            <p><strong>How it Works:</strong></p>
                            <p>
                                <strong>A - Application Collection:</strong> ATS collects job applications submitted online through job boards, company websites, or emails.
                            </p>
                            <p><strong>B - Resume Parsing:</strong> It scans resumes to extract and organize key information like contact details, work experience, education, and skills.</p>
                            <p><strong>C - Keyword Matching:</strong> The system compares resumes with job descriptions to identify candidates whose qualifications closely match the required role, which is why you need a tailored resume for each company.</p>
                            <p><strong>D - Candidate Ranking:</strong> Based on the match, the system scores or ranks resumes, highlighting the most suitable candidates for review by hiring managers.</p>
                            <p><strong>How to Pass ATS:</strong> Simply avoid using a colorful resume and use the keywords found in the job description. For example, if the job description mentions "team leader," use "team leader" in your experience section instead of "manager."</p>
                        </div>
                        <button class="read-more-btn">Read More</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Resume Guide Article -->
        <div class="contact-section fade-in">
            <div class="contact-form"> 
                <div class="contact-box">
                    <h2 class="title" id="resume-guide">Components of a Professional Resume</h2>
                    <div class="article-content">
                        <p>
                            A well-structured resume is your ticket to getting noticed by employers. Here are the essential components every professional resume should include:
                        </p>
                        <p>
                            <strong>1- Contact Information:</strong> Include your full name, phone number, professional email address, and LinkedIn profile. Make sure this information is current and easily visible at the top of your resume.
                        </p>
                        
                        <div class="hidden-content">
                            <p>
                                <strong>2- Professional Summary:</strong> A concise 2-3 sentence overview highlighting your professional background, key strengths, and career goals. This should be tailored to each position you apply for.
                            </p>
                            <div class="pic">
                                <img src="../images/resume-example.jpg" alt="Professional resume example">
                            </div>
                            <p>
                                <strong>3- Work Experience:</strong> List your work history in reverse chronological order (most recent first). For each position, include:
                            </p>
                            <ul>
                                <li>Company name and location</li>
                                <li>Your job title</li>
                                <li>Employment dates (month/year)</li>
                                <li>3-5 bullet points describing your responsibilities and achievements</li>
                                <li>Quantifiable results whenever possible (e.g., "Increased sales by 25%")</li>
                            </ul>
                            <p>
                                <strong>4- Education:</strong> Include your degrees, certifications, and relevant coursework. For recent graduates, this section may come before work experience.
                            </p>
                            <p>
                                <strong>5- Skills:</strong> List both hard skills (technical abilities) and soft skills (interpersonal qualities) relevant to the position. Be specific and honest about your proficiency levels.
                            </p>
                            <p>
                                <strong>6- Additional Sections:</strong> Depending on your field and experience, consider adding:
                            </p>
                            <ul>
                                <li>Professional certifications</li>
                                <li>Languages</li>
                                <li>Volunteer work</li>
                                <li>Projects</li>
                                <li>Publications or presentations</li>
                            </ul>
                            <p>
                                <strong>7- ATS-Friendly Formatting:</strong> Use a clean, simple design with standard fonts. Avoid tables, headers/footers, and graphics that ATS systems struggle to parse.
                            </p>
                        </div>
                        <button class="read-more-btn">Read More</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- ATS Video Guide -->
        <div class="contact-section fade-in">
            <div class="contact-form-new">
                <div class="contact-box">
                    <h2 class="title" id="video-guide">Watch the video below to learn more about ATS</h2>
                    <div class="video-container">
                        <iframe 
                            src="https://www.youtube.com/embed/fYd2i5zOZZE" 
                            title="How to Pass ATS and Get Your Dream Job" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    </div>
                    <p>Wait... that's not all, scroll down to learn more about resumes.</p>
                </div>
            </div>
        </div>

        <!-- A Hunt Service -->
        <div class="contact-form-new2 fade-in">
            <div class="contact-box">
                <div>
                  
                    <h2 class="title" id="a-hunt">Need a responsible candidate for your business? You might need A Hunt Service!</h2>
                      <div class="pic">
                        <img src="../images/apg3.jpg" alt="A Hunt Service">
                    </div>
                    <div class="article-content">
                        <p><strong>Introduction to A Hunt:</strong> A Hunt is an exclusive recruitment service designed to identify, attract, and secure top-tier talent for your organization. Using our extensive network, advanced expertise matching algorithms, and customized recruitment strategies, we ensure the delivery of the most suitable candidates for your specific needs. From search and screening to final hiring, the A Hunt team handles the entire recruitment process with precision and professionalism.</p>

                        <p><strong>Why Choose A Hunt Service?</strong></p>
                        <div class="hidden-content">
                            <p><strong>Customized Recruitment Solutions:</strong> We prioritize understanding your company's unique culture, values, and needs, ensuring candidates align with your organizational goals.</p>
                            <p><strong>Time and Cost Efficiency:</strong> Our streamlined process saves you valuable time and resources, allowing you to focus on core business activities while we handle the complexities of recruitment.</p>
                            <p><strong>Access to Exceptional Talent:</strong> By leveraging our extensive network and expertise, we provide you with a pool of highly qualified candidates who may not be on the market but are the perfect fit for your roles.</p>
                            <p><strong>Dedicated Follow-up:</strong> We stand by you until the position is successfully filled, providing continuous follow-up and support for a seamless hiring experience.</p>

                            <p><strong>Commission and Terms</strong></p>
                            <p>We offer our service on a competitive commission basis to ensure you receive exceptional value:</p>
                            <p><strong>Commission Fee:</strong> Upon successful placement of a candidate, a fee equivalent to one gross monthly salary of the hired individual is charged.</p>
                            <p><strong>Initial Payment:</strong> To initiate the recruitment process, we request an initial payment of 10% of the gross monthly salary for the required position. This payment demonstrates your commitment and allows us to allocate the necessary resources for the search process.</p>
                        </div>
                        <button class="read-more-btn">Read More</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Partners Grid Section -->
    <section class="partners-grid-section">
        <div class="container">
            <h2 class="section-title">Our Partners</h2>
            <div class="partners-grid">
                <!-- Partner 1 -->
                <div class="partner-grid-item">
                    <a href="https://www.instagram.com/maskan.architecture.egypt?igsh=YXh0ajd4dTFjcTJ4" target="_blank" rel="noopener noreferrer" aria-label="Maskan Architecture Egypt">
                        <div class="partner-logo-container">
                            <img src="../images/partner1.png" alt="Maskan Architecture Egypt" class="partner-grid-logo" loading="lazy">
                        </div>
                    </a>
                </div>
                
                <!-- Partner 2 -->
                <div class="partner-grid-item">
                    <a href="https://www.instagram.com/linkout.eg?igsh=MWVqZGVtbWJtZGVkbw==" target="_blank" rel="noopener noreferrer" aria-label="LinkOut Egypt">
                        <div class="partner-logo-container">
                            <img src="../images/partner2.png" alt="LinkOut Egypt" class="partner-grid-logo" loading="lazy">
                        </div>
                    </a>
                </div>
                
                <!-- Partner 3 -->
                <div class="partner-grid-item">
                    <a href="https://www.instagram.com/iti_egypt?igsh=MWVqZGVtbWJtZGVkbw==" target="_blank" rel="noopener noreferrer" aria-label="ITI Egypt">
                        <div class="partner-logo-container">
                            <img src="../images/partner3.png" alt="ITI Egypt" class="partner-grid-logo" loading="lazy">
                        </div>
                    </a>
                </div>
                
                <!-- Partner 4 -->
                <div class="partner-grid-item">
                    <a href="https://www.instagram.com/iti_egypt?igsh=MWVqZGVtbWJtZGVkbw==" target="_blank" rel="noopener noreferrer" aria-label="Partner 4">
                        <div class="partner-logo-container">
                            <img src="../images/partner4.png" alt="Partner 4" class="partner-grid-logo" loading="lazy">
                        </div>
                    </a>
                </div>
                
                <!-- Partner 5 -->
                <div class="partner-grid-item">
                    <a href="#" target="_blank" rel="noopener noreferrer" aria-label="Partner 5">
                        <div class="partner-logo-container">
                            <img src="../images/partner5.png" alt="Partner 5" class="partner-grid-logo" loading="lazy">
                        </div>
                    </a>
                </div>
                
                <!-- Partner 6 -->
               
            </div>
        </div>
    </section>

      <!-- Footer: Modern, Accessible, and Responsive (English) -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-brand">
                <a href="indexar.html" class="footer-logo" aria-label="Asher Jobs Home">
                    <img src="../images/logo.png" alt="Asher Jobs Logo" height="60" loading="lazy">
                    <h3>Asher Jobs</h3>
                </a>
               
                <p class="footer-tagline">Connecting Talent with Opportunity</p>
            </div>
            <nav class="footer-nav" aria-label="Footer Navigation">
                <ul class="footer-links">
                    <li><a href="../html/jobs.html">Browse Jobs</a></li>
                    <li><a href="../html/adminLogin.html">Admin Login</a></li>
                    <li><a href="../html/contact.html">Contact Us</a></li>
                    <li><a href="../html/privacy.html">Privacy Policy</a></li>
                </ul>
            </nav>
            <div class="footer-social">
                
                <div class="social-icons">
                    <a href="https://www.facebook.com/profile.php?id=61566379346391" aria-label="Facebook" class="fa fa-facebook" tabindex="0"></a>
                    <a href="https://www.linkedin.com/company/3ashr-jobs/?viewAsMember=true" aria-label="LinkedIn" class="fa fa-linkedin" tabindex="0"></a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 Asher Jobs. All rights reserved.</p>
        </div>
  <style>
    /* Basic Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.site-footer {
  background-color: #003C43;
  color: #f1f1f1;
  padding: 40px 20px 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2rem;
}

.footer-brand {
  flex: 1 1 250px;
}

.footer-brand h3 {
  font-size: 1.5rem;
  margin-top: 10px;
  color: #ffffff;
}

.footer-tagline {
  font-size: 0.95rem;
  color: #cfcfcf;
  margin-top: 5px;
}

.footer-logo img {
  max-width: 100%;
  height: 100px;
  border-radius: 5px;
}

.footer-nav {
  flex: 1 1 200px;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-top: 20px;
}

.footer-links a {
  color: #cfcfcf;
  display: inline-block;
  transition: transform 0.3s ease;
}

.footer-links a:hover,
.footer-links a:focus {
  color: #77B0AA;
  text-decoration: none;
    transform: scale(1.1);
}

.footer-social {
  flex: 1 1 200px;
  margin-top: 70px;
}

.footer-social p {
  font-weight: bold;
  margin-bottom: 10px;
}

.social-icons a {
  display: inline-block;
  font-size: 1.25rem;
  margin-right: 15px;
  color: #cfcfcf;
  transition: color 0.3s, transform 0.3s;
}

.social-icons a:hover,
.social-icons a:focus {
  color: #ffffff;
  transform: scale(1.1);
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  font-size: 0.875rem;
  color: #999;
  border-top: 1px solid #444;
  margin-top: 30px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .footer-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .footer-social, .footer-brand, .footer-nav {
    flex: 1 1 100%;
  }

  .social-icons a {
    margin: 0 10px;
  }
}

  </style>
    </footer>

    <!-- Scripts -->
    <script>
        // Add error handling to detect and report JS issues
        window.addEventListener('error', function(event) {
            console.error('JavaScript error detected:', event.error);
            
            // Make all content visible if there's a JS error
            document.querySelectorAll('.fade-in').forEach(el => {
                el.classList.add('visible');
            });
            
            // Check for specific issues
            if (event.error && event.error.message && 
                (event.error.message.includes('undefined') || 
                 event.error.message.includes('null'))) {
                console.warn('Possible DOM element not found. Ensuring all content is visible.');
                document.body.style.visibility = 'visible';
                document.body.style.opacity = '1';
            }
        });

        // Check document readiness
        document.addEventListener('readystatechange', function() {
            console.log('Document ready state:', document.readyState);
            if (document.readyState === 'complete') {
                console.log('Document fully loaded');
            }
        });

        // Toggle sitemap dropdown
        document.addEventListener('DOMContentLoaded', function() {
            const floatingSitemap = document.querySelector('.floating-sitemap');
            const toggleButton = document.querySelector('.sitemap-toggle');
            const dropdownMenu = document.querySelector('.dropdown-menu');

            // Toggle dropdown on button click
            toggleButton.addEventListener('click', function(e) {
                e.stopPropagation();
                dropdownMenu.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!floatingSitemap.contains(e.target)) {
                    dropdownMenu.classList.remove('active');
                }
            });

            // Read More buttons functionality
            const readMoreButtons = document.querySelectorAll('.read-more-btn');
            readMoreButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const hiddenContent = this.previousElementSibling;
                    hiddenContent.classList.toggle('show');
                    this.textContent = hiddenContent.classList.contains('show') ? 'Read Less' : 'Read More';
                });
            });
        });
    </script>
    <script>
        // Improved fade-in animation handler
        document.addEventListener("DOMContentLoaded", function() {
            // First make sure all content is visible in case of JS errors
            setTimeout(() => {
                document.querySelectorAll('.fade-in').forEach(el => {
                    if (!el.classList.contains('visible')) {
                        el.classList.add('visible');
                    }
                });
            }, 1000); // Fallback after 1 second
            
            // Then set up proper intersection observer
            try {
                const fadeInElements = document.querySelectorAll(".fade-in");
                
                if ('IntersectionObserver' in window) {
                    const observer = new IntersectionObserver((entries) => {
                        entries.forEach((entry) => {
                            if (entry.isIntersecting) {
                                entry.target.classList.add("visible");
                            }
                        });
                    }, {
                        threshold: 0.1,
                        rootMargin: '50px'
                    });
                    
                    fadeInElements.forEach((element) => observer.observe(element));
                } else {
                    // Fallback for browsers without IntersectionObserver
                    fadeInElements.forEach(element => element.classList.add('visible'));
                }
            } catch (error) {
                console.error("Error in fade-in animation:", error);
                // Make all elements visible if there's an error
                document.querySelectorAll('.fade-in').forEach(el => el.classList.add('visible'));
            }
        });
    </script>
    <script src="../js/partnersCarousel.js"></script>
    <script>
        // Force center alignment for all content elements
        document.addEventListener('DOMContentLoaded', function() {
            // Apply centering to all major containers
            const containers = document.querySelectorAll('.container, section, .hero-content, .article-content, .wacontainer, .hidden-content, .contact-box');
            containers.forEach(container => {
                container.style.marginLeft = 'auto';
                container.style.marginRight = 'auto';
                container.style.textAlign = 'center';
                
                // Center flex children
                if (window.getComputedStyle(container).display === 'flex') {
                    container.style.justifyContent = 'center';
                    container.style.alignItems = 'center';
                }
            });
            
            // Center all headings and paragraphs
            const textElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, .title, .section-title');
            textElements.forEach(element => {
                element.style.textAlign = 'center';
                element.style.width = '100%';
                element.style.maxWidth = '800px';
                element.style.marginLeft = 'auto';
                element.style.marginRight = 'auto';
            });
            
            // Center all buttons
            const buttons = document.querySelectorAll('.btn, button, .read-more-btn');
            buttons.forEach(button => {
                button.style.marginLeft = 'auto';
                button.style.marginRight = 'auto';
                button.style.display = 'block';
                button.style.textAlign = 'center';
            });
            
            // Center all images
            const images = document.querySelectorAll('img, .pic, .image-container');
            images.forEach(image => {
                image.style.marginLeft = 'auto';
                image.style.marginRight = 'auto';
                image.style.display = 'block';
            });
        });
    </script>
</body>
</html>
