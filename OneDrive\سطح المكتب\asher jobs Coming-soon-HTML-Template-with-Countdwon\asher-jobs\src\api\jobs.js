import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { serveStatic } from 'hono/cloudflare-workers'

const app = new Hono()

// CORS Configuration
app.use('/api/*', cors({
  origin: ['https://asher-jobs.com', 'https://www.asher-jobs.com'],
  allowHeaders: ['Content-Type', 'Authorization'],
  allowMethods: ['GET', 'POST', 'DELETE', 'PUT', 'OPTIONS'],
  maxAge: 86400,
  credentials: true
}))

// API Routes
app.get('/api/jobs', async (c) => {
  try {
    const { results } = await c.env.DB.prepare('SELECT * FROM jobs').all()
    return c.json({ success: true, data: results })
  } catch (err) {
    return c.json({ error: err.message }, 500)
  }
})

app.post('/api/analyze-job', async (c) => {
  try {
    const { description } = await c.req.json()
    const response = await c.env.AI.run('@cf/meta/llama-2-7b-chat-int8', {
      messages: [{
        role: "system",
        content: "Analyze job descriptions professionally"
      }, {
        role: "user",
        content: description
      }]
    })
    return c.json({ analysis: response.result.response })
  } catch (err) {
    return c.json({ error: err.message }, 500)
  }
})

app.post('/api/jobs/init', async (c) => {
  try {
    await c.env.DB.batch([
      c.env.DB.prepare(`CREATE TABLE IF NOT EXISTS jobs (...)`),
      c.env.DB.prepare(`INSERT INTO jobs (...) VALUES (...)`)
    ]);
    return c.json({ success: true });
  } catch (err) {
    return c.json({ error: err.message }, 500);
  }
});

app.post('/api/jobs', async (c) => {
  try {
    const jobData = await c.req.json();
    const { meta } = await c.env.DB.prepare(
      `INSERT INTO jobs (...) VALUES (...)`
    ).run(jobData);
    
    return c.json({ 
      success: true,
      id: meta.last_row_id
    }, 201);
  } catch (err) {
    return c.json({ error: err.message }, 500);
  }
});

app.get('/api/jobs/admin', async (c) => {
  try {
    const { results } = await c.env.DB.prepare(
      "SELECT * FROM jobs"
    ).all();
    return c.json(results);
  } catch (err) {
    return c.json({ error: err.message }, 500);
  }
});

// Add before your POST handler
app.options('/api/jobs', (c) => {
  return new Response(null, {
    headers: {
      'Access-Control-Allow-Origin': 'https://asher-jobs.com',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
});

app.options('/api/jobs/init', (c) => {
  return new Response(null, {
    headers: {
      'Access-Control-Allow-Origin': 'https://asher-jobs.com',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
});

// Static Assets Handling
app.get('*', serveStatic({ root: './public' }))
app.get('*', async (c) => {
  return c.env.ASSETS.fetch(c.req)
})

// HTML Fallback
app.get('/*', async (c) => {
  const url = new URL(c.req.url)
  if (!url.pathname.includes('.')) {
    return c.env.ASSETS.fetch(new Request(new URL('/index.html', c.req.url)))
  }
  return new Response(null, { status: 404 })
})

export default app 