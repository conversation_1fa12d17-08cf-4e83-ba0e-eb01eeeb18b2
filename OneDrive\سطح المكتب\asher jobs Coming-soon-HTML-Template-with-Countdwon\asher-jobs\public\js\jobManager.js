const jobManager = {
  initializeSampleJobs: async function () {
    try {
      const response = await fetch('/api/jobs/init', { method: 'POST' });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Initialization error:', error);
      return [];
    }
  },

  handleResponse: async function (response) {
    const contentType = response.headers.get('content-type');
    
    if (response.status === 405) {
      throw new Error(`Method not allowed. Check CORS configuration`);
    }

    if (!response.ok) {
      const errorText = await response.clone().text();
      throw new Error(`HTTP ${response.status}: ${errorText.slice(0, 100)}`);
    }

    if (!contentType?.includes('application/json')) {
      const text = await response.clone().text();
      console.warn('Non-JSON response:', text.slice(0, 100));
      throw new Error(`Invalid content type: ${contentType}`);
    }

    return response.json();
  },

  loadControlPanelJobs: async function () {
    try {
      const response = await fetch('/api/jobs/admin');
      return this.handleResponse(response);
    } catch (error) {
      console.error('Admin jobs error:', error);
      return [];
    }
  }
};

document.addEventListener('DOMContentLoaded', async () => {
  try {
    await jobManager.initializeSampleJobs();
  } catch (error) {
    console.error('Initialization failed:', error);
  }
}); 