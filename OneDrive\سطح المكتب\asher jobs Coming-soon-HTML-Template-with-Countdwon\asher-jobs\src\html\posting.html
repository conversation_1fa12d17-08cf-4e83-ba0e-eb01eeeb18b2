<!DOCTYPE html>
<html lang="en">
<head>
	<meta http-equiv="Content-Security-Policy" content="default-src 'self' https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pagead2.googlesyndication.com https://cdn.ampproject.org https://oss.maxcdn.com https://static.cloudflareinsights.com https://ep2.adtrafficquality.google; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https:;">
	<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2136749359648243"
     crossorigin="anonymous"></script>
		 <script async custom-element="amp-auto-ads"
        src="https://cdn.ampproject.org/v0/amp-auto-ads-0.1.js">
</script>
	<!-- Meta -->
	<metadata>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Post your job openings on Asher Jobs. Reach qualified candidates in 10th of Ramadan City." />
    <meta name="keywords" content="post jobs, job posting, hire employees, recruitment, job vacancies" />
    <meta name="author" content="Asher Jobs - وظائف العاشر" />
</metadata>
	<!-- Page Title -->
	<title data-translate="posting.title">Post a Job - Asher Jobs وظائف العاشر</title>
	<!-- Favicon -->
	<link rel="icon" href="src\images\favicon.ico" type="image/x-icon">
	<link rel="shortcut icon" href="src\images\favicon.ico" type="image/x-icon">
	<link rel="apple-touch-icon" href="src\images\favicon.ico">
	<link rel="mask-icon" href="src\images\favicon.ico" color="#003C43">
	<meta name="msapplication-TileImage" content="src\images\favicon.ico">
	<meta name="theme-color" content="#003C43">
	<!-- Google Fonts css-->
	<link href="https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700,800,900" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
	<!-- Bootstrap css -->
	
	<!-- Main custom css -->
	<link href="../css/posting.css" rel="stylesheet" media="screen">
	<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <!-- Job Manager Script -->
    <script src="../js/jobManager.js"></script>
</head>
<body>
    <!-- Navigation -->
    <header>
        <div class="nav-links">
            <a href="../../index.html">الرئيسية</a>
            <a href="jobs.html">الوظائف</a>
            <a href="posting.html" class="active">نشر وظيفة</a>
        </div>
        <div class="header-actions">
            <button id="adminButton" onclick="toggleAdminPanel()" class="admin-button">لوحة التحكم</button>
            <button id="logoutButton" onclick="logout()" class="logout-button">تسجيل الخروج</button>
        </div>
    </header>
    
    <!-- Control Panel -->
    <div id="controlPanel" class="hidden">
        <div class="control-panel">
            <h3>Job Control Panel</h3>
            <div class="control-panel-controls">
                <button onclick="loadControlPanelJobs()" class="control-refresh">Refresh</button>
            </div>
            <div class="control-jobs-list">
                <h4>Manage Posted Jobs</h4>
                <div id="controlJobsList"></div>
            </div>
        </div>
    </div>
    
    <!-- Admin Panel -->
    <div id="adminPanel" class="hidden">
        <div class="admin-panel">
            <h3>لوحة التحكم</h3>
            <div class="admin-controls">
                <button onclick="toggleAdminPanel()" class="admin-close">✕</button>
                <button onclick="window.location.href='jobs.html'" class="admin-btn">عرض الوظائف</button>
                <button onclick="logout()" class="admin-btn">تسجيل الخروج</button>
            </div>
            <div class="admin-jobs-list">
                <h4>الوظائف المنشورة</h4>
                <div id="adminJobsList"></div>
            </div>
        </div>
    </div>
    
    <script>
       const jobManager = {
    getAllJobs: () => {
        const jobs = JSON.parse(localStorage.getItem('jobs')) || [];
        return jobs;
    },
    deleteJob: (jobId) => {
        const jobs = JSON.parse(localStorage.getItem('jobs')) || [];
        const updatedJobs = jobs.filter(job => job.id !== jobId);
        localStorage.setItem('jobs', JSON.stringify(updatedJobs));
        return { success: true };
    },
    // Add other job management functions as needed
};

    


// Remove the local jobManager declaration completely
// Keep only the functions below

function toggleAdminPanel() {
    const adminPanel = document.getElementById("adminPanel");
    adminPanel.classList.toggle("hidden");
}

async function deleteJob(jobId) {
    if (confirm("Are you sure you want to delete this job?")) {
        try {
            const result = await window.jobManager.deleteJob(jobId);
            if (result.success) {
                alert("Job deleted successfully!");
                loadControlPanelJobs();
                loadAdminJobs(); // Refresh both panels
            } else {
                alert(result.error || "Failed to delete job");
            }
        } catch (error) {
            console.error("Error deleting job:", error);
            alert("An error occurred while deleting the job");
        }
    }
}

// Modify the loadControlPanelJobs() function's delete button to use the correct function
jobs.forEach((job) => {
    const jobItem = document.createElement("div");
    jobItem.className = "control-job-item";
    jobItem.innerHTML = `
        <div class="job-details">
            <h5>${job.title}</h5>
            <p>${job.company}</p>
            <small>${job.description.substring(0, 100)}...</small>
        </div>
        <div class="job-actions">
            <button class="modify-btn" onclick="modifyJob('${job.id}')">Modify</button>
            <button class="delete-btn" onclick="deleteJob('${job.id}')">Delete</button>
        </div>
    `;
    controlJobsList.appendChild(jobItem);
});

    
        function logout() {
            alert("Logged out successfully!");
            window.location.href = "index.html";
        }
    
        async function loadControlPanelJobs() {
            try {
                const jobs = await jobManager.getAllJobs();
                const controlJobsList = document.getElementById("controlJobsList");
                controlJobsList.innerHTML = "";
    
                if (!jobs || jobs.length === 0) {
                    controlJobsList.innerHTML = '<div class="no-jobs">No jobs available</div>';
                    return;
                }
    
                jobs.forEach((job) => {
                    const jobItem = document.createElement("div");
                    jobItem.className = "control-job-item";
                    jobItem.innerHTML = `
                        <div class="job-details">
                            <h5>${job.title}</h5>
                            <p>${job.company}</p>
                            <small>${job.description.substring(0, 100)}...</small>
                        </div>
                        <div class="job-actions">
                            <button class="modify-btn" onclick="modifyJob(${job.id})">Modify</button>
                            <button class="delete-btn" onclick="deleteJob(${job.id})">Delete</button>
                        </div>
                    `;
                    controlJobsList.appendChild(jobItem);
                });
            } catch (error) {
                console.error("Error loading jobs:", error);
                document.getElementById("controlJobsList").innerHTML = `<div class="error-message">Error: ${error.message}</div>`;
            }
        }
    
        function modifyJob(jobId) {
            const jobTitle = prompt("Enter the new job title:");
            const jobCompany = prompt("Enter the new company name:");
            const jobDescription = prompt("Enter the new job description:");
    
            if (jobTitle && jobCompany && jobDescription) {
                jobManager.updateJob(jobId, { title: jobTitle, company: jobCompany, description: jobDescription });
                alert("Job updated successfully!");
                loadControlPanelJobs();
            }
        }
    
        deleteJob: async (jobId) => {
    try {
        const response = await fetch(`${window.location.origin}${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.JOBS}?id=${jobId}`, {
            method: 'DELETE',
        });
        const result = await response.json();
        console.log('Response:', result);
        if (result && result.success) {
            return { success: true };
        }
        throw new Error(result?.message || 'Unexpected response format');
    } catch (error) {
        console.error('Error in deleteJob:', error);
        return { success: false, error: error.message };
    }
};
        if (confirm("Are you sure you want to delete this job?")) {
            try {
                await jobManager.deleteJob(jobId);
                alert("Job deleted successfully!");
                loadControlPanelJobs();
            } catch (error) {
                console.error("Error deleting job:", error);
                alert("An error occurred while deleting the job. Please try again.");
            }
        }
        

        // Load the control panel jobs on page load
        document.addEventListener("DOMContentLoaded", loadControlPanelJobs);
    </script>

    <div class="job-form-container">
        <h2>نشر إعلان وظيفة جديدة</h2>
        <form id="jobPostingForm">
            <div class="form-group">
                <label for="jobTitle" data-translate="posting.form.title.label">Job Title</label>
                <input type="text" id="jobTitle" name="title" class="form-control" required data-translate="posting.form.title.placeholder">
            </div>
            <div class="form-group">
                <label for="companyName" data-translate="posting.form.company.label">Company Name</label>
                <input type="text" id="companyName" name="company" class="form-control" required data-translate="posting.form.company.placeholder">
            </div>
            <div class="form-group">
                <label for="location" data-translate="posting.form.location.label">Location</label>
                <input type="text" id="location" name="location" class="form-control" required data-translate="posting.form.location.placeholder">
            </div>
            <div class="form-group">
                <label for="description" data-translate="posting.form.description.label">Job Description</label>
                <textarea id="description" name="description" class="form-control" rows="5" required data-translate="posting.form.description.placeholder"></textarea>
            </div>
            <div class="form-group">
                <label for="requirements" data-translate="posting.form.requirements.label">Requirements</label>
                <textarea id="requirements" name="requirements" class="form-control" rows="3" required data-translate="posting.form.requirements.placeholder"></textarea>
            </div>
            <div class="form-group">
                <label for="salary" data-translate="posting.form.salary.label">Salary Range</label>
                <input type="text" id="salary" name="salary" class="form-control" required data-translate="posting.form.salary.placeholder">
            </div>
            <div class="form-group">
                <label for="jobType" data-translate="posting.form.type.label">Job Type</label>
                <select id="jobType" name="type" class="form-control" required>
                    <option value="full-time">Full Time</option>
                    <option value="part-time">Part Time</option>
                    <option value="contract">Contract</option>
                    <option value="internship">Internship</option>
                </select>
            </div>
           
            <button type="submit" class="submit-button" data-translate="posting.form.submit">Post Job</button>
        </form>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if user is authenticated
        if (!sessionStorage.getItem('authenticated')) {
            // Redirect to login page if not authenticated
            window.location.href = 'adminLogin.html';
            return;
        }

        const form = document.getElementById('jobPostingForm');
        const submitButton = form.querySelector('button[type="submit"]');
        const loadingSpinner = document.createElement('span');
        loadingSpinner.className = 'spinner-border spinner-border-sm d-none';
        loadingSpinner.setAttribute('role', 'status');
        loadingSpinner.setAttribute('aria-hidden', 'true');
        submitButton.appendChild(loadingSpinner);

        // Function to show/hide loading spinner
        function toggleLoading(show) {
            loadingSpinner.classList.toggle('d-none', !show);
            submitButton.disabled = show;
            submitButton.innerHTML = show ? 
                '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...' : 
                'Post Job';
        }

        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Show loading state
            toggleLoading(true);

            // Get form data
            const jobData = {
                title: document.getElementById('jobTitle').value,
                company: document.getElementById('companyName').value,
                location: document.getElementById('location').value,
                description: document.getElementById('description').value,
                requirements: document.getElementById('requirements').value,
                salary: document.getElementById('salary').value,
                type: document.getElementById('jobType').value
            };

            try {
                console.log('Submitting job data:', jobData);
                
                // Use the addJob function from jobManager.js
                const result = await jobManager.addJob(jobData);
                console.log('Job submission result:', result);
                
                if (result && result.success) {
                    alert('Job posted successfully!');
                    
                    // Clear form
                    form.reset();
                    
                    // Redirect to the jobs page
                    window.location.href = 'jobs.html';
                } else {
                    alert(result ? (result.error || 'Failed to post job. Please try again.') : 'Failed to post job. Please try again.');
                }
            } catch (error) {
                console.error('Error submitting job:', error);
                alert('An error occurred while submitting the job. Please try again.');
            } finally {
                // Reset button state
                toggleLoading(false);
            }
        });
        
        // Load existing jobs for admin panel
        loadAdminJobs();
    });

    // Function to load admin jobs
    async function loadAdminJobs() {
        try {
            const jobs = await jobManager.getAllJobs();
            const adminJobsList = document.getElementById('adminJobsList');
            if (!adminJobsList) {
                console.error('Admin jobs list container not found');
                return;
            }
            
            adminJobsList.innerHTML = '';
            
            if (!jobs || jobs.length === 0) {
                adminJobsList.innerHTML = '<div class="no-jobs">No jobs posted yet</div>';
                return;
            }
            
            jobs.forEach((job, index) => {
                const jobItem = document.createElement('div');
                jobItem.className = 'admin-job-item';
                jobItem.innerHTML = `
                    <div class="job-info">
                        <h4>${job.title}</h4>
                        <p>${job.company}</p>
                        <p>${job.location}</p>
                        <small>${job.description.substring(0, 100)}...</small>
                    </div>
                    <button onclick="deleteJobById('${job.id}')" class="delete-btn">Delete</button>
                `;
                adminJobsList.appendChild(jobItem);
            });
        } catch (error) {
            console.error('Error loading admin jobs:', error);
            const adminJobsList = document.getElementById('adminJobsList');
            adminJobsList.innerHTML = `<div class="error-message">Error loading jobs: ${error.message}</div>`;
        }
    }

    // Function to delete a job
    async function deleteJobById(jobId) {
        if (confirm('Are you sure you want to delete this job?')) {
            try {
                const result = await jobManager.deleteJob(jobId);
                if (result.success) {
                    alert('Job deleted successfully!');
                    // Refresh the admin jobs list
                    loadAdminJobs();
                } else {
                    alert(result.error || 'Failed to delete job. Please try again.');
                }
            } catch (error) {
                console.error('Error deleting job:', error);
                alert('An error occurred while deleting the job. Please try again.');
            }
        }
    }

    // Function to toggle admin panel
    function toggleAdminPanel() {
        const panel = document.getElementById('adminPanel');
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    }

    // Function to logout
    function logout() {
        sessionStorage.removeItem('authenticated');
        window.location.href = 'adminLogin.html';
    }
    </script>
</body>
</html>