<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="تواصل مع عاشر جوبز للحصول على فرص عمل في مدينة العاشر من رمضان أو للاستفسار عن خدماتنا.">
    <meta name="keywords" content="اتصل بنا, عاشر جوبز, فرص عمل, العاشر من رمضان, توظيف">
    <meta name="author" content="عاشر جوبز">
    
    <title>اتصل بنا - عاشر جوبز</title>
    
    <!-- Favicon -->
    <link rel="icon" href="../images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../images/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="../images/favicon.ico">
    <meta name="theme-color" content="#003C43">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Main custom css -->
    <link href="../css/custom.css" rel="stylesheet" media="screen">
    <link href="../css/custom-ar.css" rel="stylesheet" media="screen">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <style>
        /* RTL and Arabic specific styles */
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            text-align: right;
        }
        
        /* Contact Form Specific Styles */
        .contact-form-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            text-align: right;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.3s;
            text-align: right;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-control.error {
            border-color: #dc3545;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: none;
            text-align: right;
        }
        
        /* Submit button styling for RTL */
        .submit-btn {
            background-color: var(--primary-color, #003C43);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            max-width: 300px;
            margin: 1rem auto;
            position: relative;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            font-family: 'Cairo', sans-serif;
        }
        
        .submit-btn:hover {
            background-color: var(--primary-dark, #002a30);
        }
        
        .submit-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-left: 0;
            margin-right: 10px; /* Changed from margin-left for RTL */
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .submit-btn {
                max-width: 100%;
            }
        }
        
        .success-message {
            display: none;
            background-color: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .contact-info-section {
            margin-top: 3rem;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        
        .contact-info-item {
            flex: 1 0 30%;
            min-width: 250px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .contact-info-item i {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .contact-info-item h3 {
            margin-bottom: 0.5rem;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .contact-form-container {
                padding: 1.5rem;
            }
            
            .contact-info-item {
                flex: 1 0 100%;
            }
        }
        
        /* RTL specific adjustments */
        .language-switcher {
            margin-right: auto;
            margin-left: 0;
        }
        
        .nav-links {
            padding-right: 0;
        }
        
        .footer-links {
            padding-right: 0;
        }
        
        small {
            text-align: right;
            display: block;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .submit-btn {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Header with Logo -->
    <header class="site-header">
        <div class="container">
            <div class="header-content">
                <div class="site-logo">
                    <a href="../html/indexar.html">
                        <img src="../images/logo.png" alt="شعار عاشر جوبز" height="100">
                    </a>
                </div>
                
                <!-- Language Switcher -->
                <div class="language-switcher">
                    <a href="contact.html" class="lang-switch-btn">
                        <span class="lang-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path fill="none" d="M0 0h24v24H0z"/>
                                <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-2.29-2.333A17.9 17.9 0 0 1 8.027 13H4.062a8.008 8.008 0 0 0 5.648 6.667zM10.03 13c.151 2.439.848 4.73 1.97 6.752A15.905 15.905 0 0 0 13.97 13h-3.94zm9.908 0h-3.965a17.9 17.9 0 0 1-1.683 6.667A8.008 8.008 0 0 0 19.938 13zM4.062 11h3.965A17.9 17.9 0 0 1 9.71 4.333 8.008 8.008 0 0 0 4.062 11zm5.969 0h3.938A15.905 15.905 0 0 0 12 4.248 15.905 15.905 0 0 0 10.03 11zm4.259-6.667A17.9 17.9 0 0 1 15.938 11h3.965a8.008 8.008 0 0 0-5.648-6.667z"/>
                            </svg>
                        </span>
                        <span class="lang-text">English</span>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="main-nav">
        <div class="container">
            <ul class="nav-links">
                <li><a href="indexar.html">الرئيسية</a></li>
                <li><a href="jobs.html">الوظائف</a></li>
                <li><a href="contactar.html" class="active">اتصل بنا</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <div class="container">
            <div class="page-header">
                <h1>اتصل بنا</h1>
                <p>هل لديك أسئلة أو تحتاج إلى مساعدة؟ نحن هنا للمساعدة. املأ النموذج أدناه وسنرد عليك في أقرب وقت ممكن.</p>
            </div>
            
            <div class="contact-form-container">
                <div id="successMessage" class="success-message">
                    شكراً لرسالتك! سنتواصل معك قريباً.
                </div>
                
                <form id="contactForm">
                    <!-- CSRF Token (hidden) -->
                    <input type="hidden" name="_csrf" id="csrfToken" value="">
                    
                    <div class="form-group">
                        <label for="fullName">الاسم الكامل *</label>
                        <input type="text" id="fullName" name="fullName" class="form-control" required>
                        <div class="error-message" id="fullNameError">يرجى إدخال الاسم الكامل</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني *</label>
                        <input type="email" id="email" name="email" class="form-control" required>
                        <div class="error-message" id="emailError">يرجى إدخال بريد إلكتروني صحيح</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">رقم الهاتف *</label>
                        <input type="tel" id="phone" name="phone" class="form-control" required placeholder="مثال: +20 ************">
                        <div class="error-message" id="phoneError">يرجى إدخال رقم هاتف صحيح</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="subject">الموضوع *</label>
                        <select id="subject" name="subject" class="form-control" required>
                            <option value="">اختر موضوع</option>
                            <option value="Job Application">طلب وظيفة</option>
                            <option value="Business Inquiry">استفسار تجاري</option>
                            <option value="Technical Support">الدعم الفني</option>
                            <option value="Feedback">تعليقات</option>
                            <option value="Other">أخرى</option>
                        </select>
                        <div class="error-message" id="subjectError">يرجى اختيار موضوع</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="message">الرسالة *</label>
                        <textarea id="message" name="message" class="form-control" rows="5" required maxlength="1000"></textarea>
                        <div class="error-message" id="messageError">يرجى إدخال رسالتك</div>
                        <small id="charCount">0/1000 حرف</small>
                    </div>
                    
                    <button type="submit" class="submit-btn" id="submitBtn">
                        <span class="spinner" id="submitSpinner"></span>
                        إرسال الرسالة
                    </button>
                </form>
            </div>
            
            <div class="contact-info-section">
                <div class="contact-info-item">
                    <i class="fa fa-envelope"></i>
                    <h3>البريد الإلكتروني</h3>
                    <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
                
                <div class="contact-info-item">
                    <i class="fa fa-phone"></i>
                    <h3>الهاتف</h3>
                    <p><a href="tel:+20156454666">+201556454666</a></p>
                </div>
                
                <div class="contact-info-item">
                    <i class="fa fa-map-marker"></i>
                    <h3>العنوان</h3>
                    <p>مدينة العاشر من رمضان، القاهرة، مصر</p>
                </div>
            </div>
        </div>
    </main>
      <!-- Footer: Modern, Accessible, and Responsive (English) -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-brand">
                <a href="../html/indexar.html" class="footer-logo" aria-label="Asher Jobs Home">
                    <img src="../images/logo.png" alt="Asher Jobs Logo" height="60" loading="lazy">
                    <h3>عاشر جوبز</h3>
                </a>
               
                <p class="footer-tagline">نربط المواهب بالفرص</p>
            </div>
            <nav class="footer-nav" aria-label="Footer Navigation">
                <ul class="footer-links">
                    <li><a href="../html/jobs.html">تصفح الوظائف</a></li>
                    <li><a href="../html/adminLogin.html">تسجيل دخول المسؤول</a></li>
                    <li><a href="../html/contact.html">تواصل معنا</a></li>
                    <li><a href="../html/privacy.html">سياسة الخصوصية</a></li>
                </ul>
            </nav>
            <div class="footer-social">
                
                <div class="social-icons">
                    <a href="https://www.facebook.com/profile.php?id=61566379346391" aria-label="Facebook" class="fa fa-facebook" tabindex="0"></a>
                    <a href="https://www.linkedin.com/company/3ashr-jobs/?viewAsMember=true" aria-label="LinkedIn" class="fa fa-linkedin" tabindex="0"></a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 Asher Jobs. All rights reserved.</p>
        </div>
  <style>
    /* Basic Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.site-footer {
  background-color: #003C43;
  color: #f1f1f1;
  padding: 40px 20px 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 2rem;
}

.footer-brand {
  flex: 1 1 250px;
}

.footer-brand h3 {
  font-size: 1.5rem;
  margin-top: 10px;
  color: #ffffff;
}

.footer-tagline {
  font-size: 0.95rem;
  color: #cfcfcf;
  margin-top: 5px;
}

.footer-logo img {
  max-width: 100%;
  height: 100px;
  border-radius: 5px;
}

.footer-nav {
  flex: 1 1 200px;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-top: 20px;
}

.footer-links a {
  color: #cfcfcf;
  display: inline-block;
  transition: transform 0.3s ease;
}

.footer-links a:hover,
.footer-links a:focus {
  color: #77B0AA;
  text-decoration: none;
    transform: scale(1.1);
}

.footer-social {
  flex: 1 1 200px;
  margin-top: 70px;
}

.footer-social p {
  font-weight: bold;
  margin-bottom: 10px;
}

.social-icons a {
  display: inline-block;
  font-size: 1.25rem;
  margin-right: 15px;
  color: #cfcfcf;
  transition: color 0.3s, transform 0.3s;
}

.social-icons a:hover,
.social-icons a:focus {
  color: #ffffff;
  transform: scale(1.1);
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  font-size: 0.875rem;
  color: #999;
  border-top: 1px solid #444;
  margin-top: 30px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .footer-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .footer-social, .footer-brand, .footer-nav {
    flex: 1 1 100%;
  }

  .social-icons a {
    margin: 0 10px;
  }
}

  </style>
    </footer>


    <!-- Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Generate CSRF token
            const csrfToken = generateCSRFToken();
            document.getElementById('csrfToken').value = csrfToken;
            
            // Form elements
            const contactForm = document.getElementById('contactForm');
            const fullNameInput = document.getElementById('fullName');
            const emailInput = document.getElementById('email');
            const phoneInput = document.getElementById('phone');
            const subjectSelect = document.getElementById('subject');
            const messageTextarea = document.getElementById('message');
            const submitBtn = document.getElementById('submitBtn');
            const submitSpinner = document.getElementById('submitSpinner');
            const successMessage = document.getElementById('successMessage');
            const charCount = document.getElementById('charCount');
            
            // Error message elements
            const fullNameError = document.getElementById('fullNameError');
            const emailError = document.getElementById('emailError');
            const phoneError = document.getElementById('phoneError');
            const subjectError = document.getElementById('subjectError');
            const messageError = document.getElementById('messageError');
            
            // Character counter for message
            messageTextarea.addEventListener('input', function() {
                const currentLength = this.value.length;
                charCount.textContent = `${currentLength}/1000 حرف`;
            });
            
            // Form validation
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Reset error states
                resetErrors();
                
                // Validate form
                let isValid = true;
                
                // Validate full name
                if (!fullNameInput.value.trim()) {
                    showError(fullNameInput, fullNameError);
                    isValid = false;
                }
                
                // Validate email
                if (!isValidEmail(emailInput.value)) {
                    showError(emailInput, emailError);
                    isValid = false;
                }
                
                // Validate phone
                if (!isValidPhone(phoneInput.value)) {
                    showError(phoneInput, phoneError);
                    isValid = false;
                }
                
                // Validate subject
                if (!subjectSelect.value) {
                    showError(subjectSelect, subjectError);
                    isValid = false;
                }
                
                // Validate message
                if (!messageTextarea.value.trim()) {
                    showError(messageTextarea, messageError);
                    isValid = false;
                }
                
                // If form is valid, submit it
                if (isValid) {
                    submitForm();
                }
            });
            
            // Helper functions
            function resetErrors() {
                const errorElements = document.querySelectorAll('.error-message');
                const formControls = document.querySelectorAll('.form-control');
                
                errorElements.forEach(el => el.style.display = 'none');
                formControls.forEach(el => el.classList.remove('error'));
            }
            
            function showError(input, errorElement) {
                input.classList.add('error');
                errorElement.style.display = 'block';
            }
            
            function isValidEmail(email) {
                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailPattern.test(email);
            }
            
            function isValidPhone(phone) {
                const phonePattern = /^\+20\s?\d{10}$/;
                return phonePattern.test(phone);
            }
            
            function submitForm() {
                submitBtn.disabled = true;
                submitSpinner.style.display = 'inline-block';
                
                const formData = {
                    name: fullNameInput.value,
                    email: emailInput.value,
                    phone: phoneInput.value,
                    subject: subjectSelect.value,
                    message: messageTextarea.value,
                    _captcha: false,
                    _template: 'table'
                };
                
                fetch('https://formsubmit.co/<EMAIL>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Show success message
                    successMessage.style.display = 'block';
                    
                    // Reset form
                    contactForm.reset();
                    charCount.textContent = '0/1000 حرف';
                    
                    // Scroll to success message
                    successMessage.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    
                    // Hide success message after 5 seconds
                    setTimeout(() => {
                        successMessage.style.display = 'none';
                    }, 5000);
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.');
                })
                .finally(() => {
                    // Reset button state
                    submitBtn.disabled = false;
                    submitSpinner.style.display = 'none';
                });
            }
            
            function generateCSRFToken() {
                // Generate a random token (in a real app, this would come from the server)
                return Math.random().toString(36).substring(2, 15) + 
                       Math.random().toString(36).substring(2, 15);
            }
        });
    </script>
</body>
</html>










