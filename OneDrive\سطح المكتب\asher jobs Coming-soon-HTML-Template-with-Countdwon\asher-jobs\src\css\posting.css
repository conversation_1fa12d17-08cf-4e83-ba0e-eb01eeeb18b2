.admin-panel {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin: 20px auto;
  max-width: 800px;
  direction: rtl;
}

.admin-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.admin-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.admin-close {
  padding: 8px 16px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.admin-job-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.delete-btn {
  padding: 5px 10px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.job-info {
  flex: 1;
}

.job-info h4 {
  margin: 0 0 5px 0;
}

.job-info p {
  margin: 0 0 5px 0;
  color: #666;
}

.job-info small {
  color: #999;
}

        /* General Styles */
        body {
          font-family: 'Cairo', 'Montserrat', sans-serif;
          margin: 0;
          padding: 0;
          background-color: #f7f7f7;
          color: #333;
      }
      
      h2, h3, h4 {
          color: #003C43;
      }
      
      a {
          text-decoration: none;
          color: inherit;
      }
      
      header {
          background-color: #003C43;
          color: white;
          padding: 15px;
      }
      
      header .nav-links {
          display: flex;
          justify-content: space-around;
          align-items: center;
      }
      
      header .nav-links a {
          color: white;
          font-weight: bold;
          padding: 10px 20px;
          border-radius: 5px;
          transition: background-color 0.3s;
      }
      
      header .nav-links a:hover,
      header .nav-links a.active {
          background-color: #4CAF50;
      }
      
      .job-form-container {
          max-width: 800px;
          margin: 20px auto;
          background: white;
          border-radius: 8px;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
          padding: 20px;
      }
      
      .job-form-container h2 {
          text-align: center;
          margin-bottom: 20px;
      }
      
      .form-group {
          margin-bottom: 15px;
      }
      
      .form-group label {
          font-weight: 600;
          display: block;
          margin-bottom: 5px;
      }
      
      .form-group input,
      .form-group textarea,
      .form-group select {
          width: 100%;
          padding: 10px;
          border: 1px solid #ccc;
          border-radius: 5px;
          font-size: 14px;
      }
      
      .submit-button {
          background-color: #003C43;
          color: white;
          font-weight: bold;
          padding: 10px 20px;
          border: none;
          border-radius: 5px;
          cursor: pointer;
          display: block;
          width: 100%;
          margin-top: 15px;
          transition: background-color 0.3s;
      }
      
      .submit-button:hover {
          background-color: #4CAF50;
      }
      
      /* Admin Panel Styles */
      #adminPanel {
          padding: 20px;
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          max-width: 800px;
          margin: 20px auto;
          direction: rtl;
      }
      
      .admin-panel h3 {
          text-align: center;
          margin-bottom: 20px;
      }
      
      .admin-controls {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
      }
      
      .admin-controls button {
          padding: 10px 20px;
          border: none;
          border-radius: 5px;
          cursor: pointer;
      }
      
      .admin-controls .admin-close {
          background-color: #dc3545;
          color: white;
      }
      
      .admin-controls .admin-btn {
          background-color: #007bff;
          color: white;
      }
      
      .admin-jobs-list {
          margin-top: 20px;
      }
      
      .admin-job-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px;
          border-bottom: 1px solid #eee;
      }
      
      .admin-job-item .job-info h4 {
          margin: 0;
          color: #003C43;
      }
      
      .admin-job-item .job-info p {
          margin: 5px 0;
          font-size: 14px;
          color: #666;
      }
      
      .delete-btn {
          background-color: #dc3545;
          color: white;
          padding: 5px 10px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
      }
      
      .delete-btn:hover {
          background-color: #bd2130;
      }
      
      /* Responsive Design */
      @media (max-width: 768px) {
          header .nav-links {
              flex-direction: column;
              text-align: center;
          }
      
          .admin-controls {
              flex-direction: column;
          }
      
          .admin-controls button {
              width: 100%;
              margin-bottom: 10px;
          }
      
          .admin-job-item {
              flex-direction: column;
              align-items: flex-start;
          }
      }
      
          footer {
              background: #003C43;
              color: #fff;
              padding: 40px 0;
              border-top: 3px solid #77B0AA;
              margin-top: 50px;
              width: 100%;
              text-align: left;
          }
      
          .footer-container {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              max-width: 1200px;
              margin: 0 auto;
              padding: 0 20px;
              direction: ltr;
              text-align: left;
          }
      
          .footer-container section {
              flex: 1;
              margin: 0 20px;
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              text-align: left;
          }
      
          .company-logo {
              text-align: left;
              justify-content: flex-start;
          }
      
          .company-logo h2 {
              margin-top: 10px;
              font-size: 24px;
              text-align: left;
          }
      
          .links h4, .follow-us h4 {
              margin-bottom: 15px;
              font-size: 18px;
              text-align: left;
              width: 100%;
          }
      
          .links-list {
              display: flex;
              flex-direction: column;
              gap: 10px;
              width: 100%;
              text-align: left;
          }
      
          .links-list a {
              color: white;
              text-decoration: none;
              transition: color 0.3s ease;
              padding: 5px 0;
              text-align: left;
              display: block;
          }
      
          .links-list a:hover {
              color: #77B0AA;
          }
      
          .social-links {
              display: flex;
              gap: 15px;
              margin-bottom: 20px;
              justify-content: flex-start;
              width: 100%;
          }
      
          .social-links a {
              transition: transform 0.3s ease;
              display: flex;
              align-items: center;
              justify-content: center;
          }
      
          .social-links a:hover {
              transform: translateY(-3px);
          }
      
          .location {
              margin-top: 20px;
              width: 100%;
              text-align: left;
          }
      
          .location h4 {
              margin-bottom: 10px;
              text-align: left;
              width: 100%;
          }
      
          .location p {
              margin: 0;
              color: #ccc;
              text-align: left;
          }
      
          .footer-container span {
              display: block;
              margin-top: 20px;
              font-size: 14px;
              color: #ccc;
              text-align: left;
              width: 100%;
          }
      
          @media (max-width: 768px) {
              .footer-container {
                  flex-direction: column;
                  text-align: center;
              }
      
              .footer-container section {
                  margin: 20px 0;
                  align-items: center;
                  text-align: center;
              }
      
              .company-logo {
                  text-align: center;
              }
      
              .social-links {
                  justify-content: center;
              }
      
              .links h4, .follow-us h4, .location h4, .footer-container span {
                  text-align: center;
              }
          }
      
          .control-panel {
          background: #f9f9f9;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          margin: 20px auto;
          max-width: 800px;
      }
      
      .control-panel-controls {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
      }
      
      .control-refresh {
          padding: 8px 16px;
          background: #28a745;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
      }
      
      .control-job-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px;
          border-bottom: 1px solid #ddd;
      }
      
      .modify-btn, .delete-btn {
          padding: 6px 12px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          color: white;
      }
      
      .modify-btn {
          background: #ffc107;
      }
      
      .delete-btn {
          background: #dc3545;
      }
      