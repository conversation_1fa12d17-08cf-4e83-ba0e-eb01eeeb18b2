import { Hono } from 'hono'
import { cors } from 'hono/cors'

const app = new Hono()

// CORS middleware
app.use('/api/*', cors({
  origin: ['https://asher-jobs.com', 'https://www.asher-jobs.com'],
  allowHeaders: ['Content-Type', 'Authorization'],
  allowMethods: ['GET', 'POST', 'DELETE', 'OPTIONS'],
  maxAge: 86400
}))

app.get('/api/jobs', async (c) => {
  try {
    const { results } = await c.env.DB.prepare('SELECT * FROM jobs').all()
    return c.json({ success: true, data: results })
  } catch (err) {
    return c.json({ error: err.message }, 500)
  }
})

// Add AI integration example
app.post('/api/analyze-job', async (c) => {
  try {
    if (!c.env.AI) {
      throw new Error('AI service not configured');
    }

    const { description } = await c.req.json();
    
    // Use the current Workers AI API format
    const response = await c.env.AI.run(
      '@cf/meta/llama-2-7b-chat-int8',
      {
        messages: [
          {
            role: "system",
            content: "You are a helpful job description analyzer"
          },
          {
            role: "user",
            content: `Analyze this job description: ${description}`
          }
        ]
      }
    );

    return c.json({
      success: true,
      analysis: response.response
    });

  } catch (err) {
    console.error('AI Error:', err);
    return c.json({
      success: false,
      error: err.message
    }, 500);
  }
});

// Static assets handling
app.get('*', async (c) => {
  return await c.env.ASSETS.fetch(c.req)
})

export default app 