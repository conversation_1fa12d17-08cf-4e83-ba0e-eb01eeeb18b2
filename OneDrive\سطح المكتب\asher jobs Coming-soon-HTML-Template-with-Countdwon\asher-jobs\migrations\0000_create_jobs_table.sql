-- Migration: Create jobs table
-- Description: Creates the jobs table for storing job listings

-- Drop the table if it exists
DROP TABLE IF EXISTS jobs;

-- Create the jobs table
CREATE TABLE jobs (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  company TEXT NOT NULL,
  location TEXT NOT NULL,
  description TEXT NOT NULL,
  salary TEXT NOT NULL,
  type TEXT NOT NULL DEFAULT 'full-time',
  requirements TEXT,
  posted_date TEXT NOT NULL,
  created_at TEXT NOT NULL
);

-- Create indexes for better search performance
CREATE INDEX idx_jobs_title ON jobs(title);
CREATE INDEX idx_jobs_company ON jobs(company);
CREATE INDEX idx_jobs_location ON jobs(location);
CREATE INDEX idx_jobs_type ON jobs(type);
CREATE INDEX idx_jobs_posted_date ON jobs(posted_date); 